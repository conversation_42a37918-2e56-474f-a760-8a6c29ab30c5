<template>
  <div class="min-h-screen bg-default">
    <!-- Sidebar -->
    <div class="fixed inset-y-0 left-0 z-50 w-64 bg-elevated border-r border-default">
      <!-- Sidebar Header -->
      <div class="flex items-center h-16 px-4 border-b border-default">
        <img 
          src="https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=40" 
          alt="Logo"
          class="w-8 h-8 rounded"
        >
        <div class="ml-3">
          <h1 class="text-lg font-semibold text-highlighted">
            ระบบจัดการ CCTV
          </h1>
        </div>
      </div>

      <!-- Navigation Menu -->
      <nav class="flex-1 px-2 py-4 space-y-1 overflow-y-auto">
        <!-- Dashboard -->
        <NuxtLink
          to="/"
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accented transition-colors"
          :class="$route.path === '/' ? 'bg-primary/10 text-primary' : 'text-default'"
        >
          <UIcon name="i-lucide-layout-dashboard" class="w-5 h-5 mr-3" />
          <span>{{ locale === 'th' ? 'ภาพรวมระบบ' : 'System Overview' }}</span>
        </NuxtLink>

        <!-- Users -->
        <NuxtLink
          to="/users/users"
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accented transition-colors"
          :class="$route.path.startsWith('/users') ? 'bg-primary/10 text-primary' : 'text-default'"
        >
          <UIcon name="i-lucide-users" class="w-5 h-5 mr-3" />
          <span>{{ locale === 'th' ? 'ผู้ใช้งานระบบ' : 'System Users' }}</span>
        </NuxtLink>

        <!-- Organizations -->
        <NuxtLink
          to="/organizations/organization"
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accented transition-colors"
          :class="$route.path.startsWith('/organizations') ? 'bg-primary/10 text-primary' : 'text-default'"
        >
          <UIcon name="i-lucide-building" class="w-5 h-5 mr-3" />
          <span>{{ locale === 'th' ? 'องค์กร' : 'Organizations' }}</span>
        </NuxtLink>

        <!-- Settings CCTV -->
        <NuxtLink
          to="/settings/cctv/camera"
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accented transition-colors"
          :class="$route.path.startsWith('/settings') ? 'bg-primary/10 text-primary' : 'text-default'"
        >
          <UIcon name="i-lucide-camera" class="w-5 h-5 mr-3" />
          <span>{{ locale === 'th' ? 'ตั้งค่า CCTV' : 'CCTV Settings' }}</span>
        </NuxtLink>

        <!-- AI Check -->
        <NuxtLink
          to="/aicheck/logcheck"
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accented transition-colors"
          :class="$route.path.startsWith('/aicheck') ? 'bg-primary/10 text-primary' : 'text-default'"
        >
          <UIcon name="i-lucide-brain-circuit" class="w-5 h-5 mr-3" />
          <span>{{ locale === 'th' ? 'ตรวจสอบ AI Log' : 'AI Log Check' }}</span>
        </NuxtLink>

        <!-- Dashboard (หากต้องการแยก) -->
        <NuxtLink
          to="/dashboard"
          class="flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-accented transition-colors"
          :class="$route.path === '/dashboard' ? 'bg-primary/10 text-primary' : 'text-default'"
        >
          <UIcon name="i-lucide-bar-chart-3" class="w-5 h-5 mr-3" />
          <span>{{ locale === 'th' ? 'Dashboard' : 'Dashboard' }}</span>
        </NuxtLink>
      </nav>

      <!-- User Profile -->
      <div class="border-t border-default p-4">
        <div class="flex items-center">
          <UAvatar 
            src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150"
            alt="วิชัย ใจดี"
            size="sm"
          />
          <div class="ml-3 min-w-0 flex-1">
            <p class="text-sm font-medium text-highlighted truncate">
              วิชัย ใจดี
            </p>
            <p class="text-xs text-muted truncate">
              ผู้ดูแลระบบ
            </p>
          </div>
          <UButton
            icon="i-lucide-log-out"
            variant="ghost"
            size="sm"
            title="ออกจากระบบ"
          />
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="ml-64">
      <!-- Top Header -->
      <header class="bg-elevated border-b border-default sticky top-0 z-40">
        <div class="flex items-center justify-between h-16 px-6">
          <!-- Page Title -->
          <div class="flex items-center">
            <h2 class="text-xl font-semibold text-highlighted">
              {{ pageTitle }}
            </h2>
          </div>

          <!-- Right Side Actions -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <div class="relative">
              <UButton
                icon="i-lucide-bell"
                variant="ghost"
                size="sm"
                title="แจ้งเตือน"
              />
              <span class="absolute -top-1 -right-1 w-2 h-2 bg-error rounded-full"></span>
            </div>

            <!-- Language Switcher -->
            <UDropdownMenu :items="languageItems">
              <UButton
                icon="i-lucide-languages"
                variant="ghost"
                size="sm"
                title="เปลี่ยนภาษา"
              />
            </UDropdownMenu>

            <!-- Theme Switcher -->
            <UButton
              :icon="$colorMode.value === 'dark' ? 'i-lucide-sun' : 'i-lucide-moon'"
              variant="ghost"
              size="sm"
              @click="toggleColorMode"
              title="เปลี่ยนธีม"
            />
          </div>
        </div>
      </header>

      <!-- Page Content -->
      <main class="p-6">
        <slot />
      </main>
    </div>
  </div>
</template>

<script setup>
const { locale, setLocale } = useI18n()
const colorMode = useColorMode()
const route = useRoute()

// Page Title based on current route
const pageTitle = computed(() => {
  switch (route.path) {
    case '/':
      return locale.value === 'th' ? 'ภาพรวมระบบ' : 'System Overview'
    case '/dashboard':
      return locale.value === 'th' ? 'Dashboard' : 'Dashboard'
    case '/users/users':
      return locale.value === 'th' ? 'ผู้ใช้งานระบบ' : 'System Users'
    case '/organizations/organization':
      return locale.value === 'th' ? 'องค์กร' : 'Organizations'
    case '/settings/cctv/camera':
      return locale.value === 'th' ? 'ตั้งค่า CCTV' : 'CCTV Settings'
    case '/aicheck/logcheck':
      return locale.value === 'th' ? 'ตรวจสอบ AI Log' : 'AI Log Check'
    default:
      return locale.value === 'th' ? 'ภาพรวมระบบ' : 'System Overview'
  }
})

// Language switcher items
const languageItems = computed(() => [
  [{
    label: 'ไทย',
    icon: locale.value === 'th' ? 'i-lucide-check' : undefined,
    onClick: () => setLocale('th')
  }],
  [{
    label: 'English', 
    icon: locale.value === 'en' ? 'i-lucide-check' : undefined,
    onClick: () => setLocale('en')
  }]
])

// Methods
function toggleColorMode() {
  colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}
</script>

<style scoped>
/* Sidebar transition effects */
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}
</style>