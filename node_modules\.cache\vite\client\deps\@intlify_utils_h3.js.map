{"version": 3, "sources": ["../../../../@intlify/utils/dist/shared/utils.9f8159f5.mjs", "../../../../@intlify/utils/dist/h3.mjs"], "sourcesContent": ["const objectToString = Object.prototype.toString;\nconst toTypeString = (value) => objectToString.call(value);\nfunction isURL(val) {\n  return toTypeString(val) === \"[object URL]\";\n}\nfunction isURLSearchParams(val) {\n  return toTypeString(val) === \"[object URLSearchParams]\";\n}\nfunction isLocale(val) {\n  return toTypeString(val) === \"[object Intl.Locale]\";\n}\nfunction toLocale(val) {\n  return isLocale(val) ? val : new Intl.Locale(val);\n}\nfunction validateLangTag(lang) {\n  try {\n    Intl.getCanonicalLocales(lang);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction parseAcceptLanguage(value) {\n  return value.split(\",\").map((tag) => tag.split(\";\")[0]).filter(\n    (tag) => !(tag === \"*\" || tag === \"\")\n  );\n}\nfunction normalizeLanguageName(langName) {\n  const [lang] = langName.split(\".\");\n  return lang.replace(/_/g, \"-\");\n}\nfunction createPathIndexLanguageParser(index = 0) {\n  return (path) => {\n    const rawPath = typeof path === \"string\" ? path : path.pathname;\n    const normalizedPath = rawPath.split(\"?\")[0];\n    const parts = normalizedPath.split(\"/\");\n    if (parts[0] === \"\") {\n      parts.shift();\n    }\n    return parts.length > index ? parts[index] || \"\" : \"\";\n  };\n}\nlet pathLanguageParser = /* @__PURE__ */ createPathIndexLanguageParser();\nfunction registerPathLanguageParser(parser) {\n  pathLanguageParser = parser;\n}\n\nconst DEFAULT_LANG_TAG = \"en-US\";\nconst DEFAULT_COOKIE_NAME = \"i18n_locale\";\nconst ACCEPT_LANGUAGE_HEADER = \"accept-language\";\n\nfunction parseDefaultHeader(input) {\n  return [input];\n}\nfunction getHeaderLanguagesWithGetter(getter, {\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  const langString = getter();\n  return langString ? name === ACCEPT_LANGUAGE_HEADER ? parser === parseDefaultHeader ? parseAcceptLanguage(langString) : parser(langString) : parser(langString) : [];\n}\nfunction getLocaleWithGetter(getter) {\n  return toLocale(getter());\n}\nfunction validateLocale(locale) {\n  if (!(isLocale(locale) || typeof locale === \"string\" && validateLangTag(locale))) {\n    throw new SyntaxError(`locale is invalid: ${locale.toString()}`);\n  }\n}\nfunction mapToLocaleFromLanguageTag(getter, ...args) {\n  return Reflect.apply(getter, null, args).map(\n    (lang) => getLocaleWithGetter(() => lang)\n  );\n}\nfunction getExistCookies(name, getter) {\n  let setCookies = getter();\n  if (!Array.isArray(setCookies)) {\n    setCookies = [setCookies];\n  }\n  setCookies = setCookies.filter(\n    (cookieValue) => cookieValue && !cookieValue.startsWith(name + \"=\")\n  );\n  return setCookies;\n}\nfunction getPathLanguage(path, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {\n  return (parser || pathLanguageParser)(path) || lang;\n}\nfunction getPathLocale(path, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {\n  return new Intl.Locale(getPathLanguage(path, { lang, parser }));\n}\nfunction getURLSearchParams(input) {\n  if (isURLSearchParams(input)) {\n    return input;\n  } else if (isURL(input)) {\n    return input.searchParams;\n  } else {\n    return new URLSearchParams(input);\n  }\n}\nfunction getQueryLanguage(query, { lang = DEFAULT_LANG_TAG, name = \"lang\" } = {}) {\n  const queryParams = getURLSearchParams(query);\n  return queryParams.get(name) || lang;\n}\nfunction getQueryLocale(query, { lang = DEFAULT_LANG_TAG, name = \"locale\" } = {}) {\n  return new Intl.Locale(getQueryLanguage(query, { lang, name }));\n}\n\nexport { ACCEPT_LANGUAGE_HEADER as A, DEFAULT_LANG_TAG as D, getLocaleWithGetter as a, DEFAULT_COOKIE_NAME as b, getExistCookies as c, getPathLocale as d, pathLanguageParser as e, getQueryLocale as f, getHeaderLanguagesWithGetter as g, createPathIndexLanguageParser as h, isLocale as i, parseAcceptLanguage as j, validateLangTag as k, mapToLocaleFromLanguageTag as m, normalizeLanguageName as n, parseDefaultHeader as p, registerPathLanguageParser as r, validateLocale as v };\n", "import { g as getHeaderLanguagesWithGetter, A as ACCEPT_LANGUAGE_HEADER, p as parseDefaultHeader, m as mapToLocaleFromLanguageTag, a as getLocaleWithGetter, D as DEFAULT_LANG_TAG, b as DEFAULT_COOKIE_NAME, v as validateLocale, d as getPathLocale$1, e as pathLanguageParser, f as getQueryLocale$1 } from './shared/utils.9f8159f5.mjs';\nimport { setCookie, getRequestURL, getCookie, getHeaders } from 'h3';\n\nfunction getHeaderLanguages(event, {\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  const getter = () => {\n    const headers = getHeaders(event);\n    return headers[name];\n  };\n  return getHeaderLanguagesWithGetter(getter, { name, parser });\n}\nfunction getHeaderLanguage(event, {\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  return getHeaderLanguages(event, { name, parser })[0] || \"\";\n}\nfunction getHeaderLocales(event, {\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  return mapToLocaleFromLanguageTag(getHeaderLanguages, event, { name, parser });\n}\nfunction tryHeaderLocales(event, {\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  try {\n    return getHeaderLocales(event, { name, parser });\n  } catch {\n    return null;\n  }\n}\nfunction getHeaderLocale(event, {\n  lang = DEFAULT_LANG_TAG,\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  return getLocaleWithGetter(() => getHeaderLanguages(event, { name, parser })[0] || lang);\n}\nfunction tryHeaderLocale(event, {\n  lang = DEFAULT_LANG_TAG,\n  name = ACCEPT_LANGUAGE_HEADER,\n  parser = parseDefaultHeader\n} = {}) {\n  try {\n    return getHeaderLocale(event, { lang, name, parser });\n  } catch {\n    return null;\n  }\n}\nfunction getCookieLocale(event, { lang = DEFAULT_LANG_TAG, name = DEFAULT_COOKIE_NAME } = {}) {\n  return getLocaleWithGetter(() => getCookie(event, name) || lang);\n}\nfunction tryCookieLocale(event, { lang = DEFAULT_LANG_TAG, name = DEFAULT_COOKIE_NAME } = {}) {\n  try {\n    return getCookieLocale(event, { lang, name });\n  } catch {\n    return null;\n  }\n}\nfunction setCookieLocale(event, locale, options = { name: DEFAULT_COOKIE_NAME }) {\n  validateLocale(locale);\n  setCookie(event, options.name, locale.toString(), options);\n}\nfunction getPathLocale(event, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {\n  return getPathLocale$1(getRequestURL(event), { lang, parser });\n}\nfunction tryPathLocale(event, { lang = DEFAULT_LANG_TAG, parser = pathLanguageParser } = {}) {\n  try {\n    return getPathLocale(event, { lang, parser });\n  } catch {\n    return null;\n  }\n}\nfunction getQueryLocale(event, { lang = DEFAULT_LANG_TAG, name = \"locale\" } = {}) {\n  return getQueryLocale$1(getRequestURL(event), { lang, name });\n}\nfunction tryQueryLocale(event, { lang = DEFAULT_LANG_TAG, name = \"locale\" } = {}) {\n  try {\n    return getQueryLocale(event, { lang, name });\n  } catch {\n    return null;\n  }\n}\n\nexport { getCookieLocale, getHeaderLanguage, getHeaderLanguages, getHeaderLocale, getHeaderLocales, getPathLocale, getQueryLocale, setCookieLocale, tryCookieLocale, tryHeaderLocale, tryHeaderLocales, tryPathLocale, tryQueryLocale };\n"], "mappings": ";;;AAAA,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,eAAe,CAAC,UAAU,eAAe,KAAK,KAAK;AACzD,SAAS,MAAM,KAAK;AAClB,SAAO,aAAa,GAAG,MAAM;AAC/B;AACA,SAAS,kBAAkB,KAAK;AAC9B,SAAO,aAAa,GAAG,MAAM;AAC/B;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,aAAa,GAAG,MAAM;AAC/B;AACA,SAAS,SAAS,KAAK;AACrB,SAAO,SAAS,GAAG,IAAI,MAAM,IAAI,KAAK,OAAO,GAAG;AAClD;AACA,SAAS,gBAAgB,MAAM;AAC7B,MAAI;AACF,SAAK,oBAAoB,IAAI;AAC7B,WAAO;AAAA,EACT,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAAS,oBAAoB,OAAO;AAClC,SAAO,MAAM,MAAM,GAAG,EAAE,IAAI,CAAC,QAAQ,IAAI,MAAM,GAAG,EAAE,CAAC,CAAC,EAAE;AAAA,IACtD,CAAC,QAAQ,EAAE,QAAQ,OAAO,QAAQ;AAAA,EACpC;AACF;AAKA,SAAS,8BAA8B,QAAQ,GAAG;AAChD,SAAO,CAAC,SAAS;AACf,UAAM,UAAU,OAAO,SAAS,WAAW,OAAO,KAAK;AACvD,UAAM,iBAAiB,QAAQ,MAAM,GAAG,EAAE,CAAC;AAC3C,UAAM,QAAQ,eAAe,MAAM,GAAG;AACtC,QAAI,MAAM,CAAC,MAAM,IAAI;AACnB,YAAM,MAAM;AAAA,IACd;AACA,WAAO,MAAM,SAAS,QAAQ,MAAM,KAAK,KAAK,KAAK;AAAA,EACrD;AACF;AACA,IAAI,qBAAqC,8BAA8B;AAKvE,IAAM,mBAAmB;AACzB,IAAM,sBAAsB;AAC5B,IAAM,yBAAyB;AAE/B,SAAS,mBAAmB,OAAO;AACjC,SAAO,CAAC,KAAK;AACf;AACA,SAAS,6BAA6B,QAAQ;AAAA,EAC5C,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,QAAM,aAAa,OAAO;AAC1B,SAAO,aAAa,SAAS,yBAAyB,WAAW,qBAAqB,oBAAoB,UAAU,IAAI,OAAO,UAAU,IAAI,OAAO,UAAU,IAAI,CAAC;AACrK;AACA,SAAS,oBAAoB,QAAQ;AACnC,SAAO,SAAS,OAAO,CAAC;AAC1B;AACA,SAAS,eAAe,QAAQ;AAC9B,MAAI,EAAE,SAAS,MAAM,KAAK,OAAO,WAAW,YAAY,gBAAgB,MAAM,IAAI;AAChF,UAAM,IAAI,YAAY,sBAAsB,OAAO,SAAS,CAAC,EAAE;AAAA,EACjE;AACF;AACA,SAAS,2BAA2B,WAAW,MAAM;AACnD,SAAO,QAAQ,MAAM,QAAQ,MAAM,IAAI,EAAE;AAAA,IACvC,CAAC,SAAS,oBAAoB,MAAM,IAAI;AAAA,EAC1C;AACF;AAWA,SAAS,gBAAgB,MAAM,EAAE,OAAO,kBAAkB,SAAS,mBAAmB,IAAI,CAAC,GAAG;AAC5F,UAAQ,UAAU,oBAAoB,IAAI,KAAK;AACjD;AACA,SAAS,cAAc,MAAM,EAAE,OAAO,kBAAkB,SAAS,mBAAmB,IAAI,CAAC,GAAG;AAC1F,SAAO,IAAI,KAAK,OAAO,gBAAgB,MAAM,EAAE,MAAM,OAAO,CAAC,CAAC;AAChE;AACA,SAAS,mBAAmB,OAAO;AACjC,MAAI,kBAAkB,KAAK,GAAG;AAC5B,WAAO;AAAA,EACT,WAAW,MAAM,KAAK,GAAG;AACvB,WAAO,MAAM;AAAA,EACf,OAAO;AACL,WAAO,IAAI,gBAAgB,KAAK;AAAA,EAClC;AACF;AACA,SAAS,iBAAiB,OAAO,EAAE,OAAO,kBAAkB,OAAO,OAAO,IAAI,CAAC,GAAG;AAChF,QAAM,cAAc,mBAAmB,KAAK;AAC5C,SAAO,YAAY,IAAI,IAAI,KAAK;AAClC;AACA,SAAS,eAAe,OAAO,EAAE,OAAO,kBAAkB,OAAO,SAAS,IAAI,CAAC,GAAG;AAChF,SAAO,IAAI,KAAK,OAAO,iBAAiB,OAAO,EAAE,MAAM,KAAK,CAAC,CAAC;AAChE;;;ACxGA,SAAS,WAAW,eAAe,WAAW,kBAAkB;AAEhE,SAAS,mBAAmB,OAAO;AAAA,EACjC,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,QAAM,SAAS,MAAM;AACnB,UAAM,UAAU,WAAW,KAAK;AAChC,WAAO,QAAQ,IAAI;AAAA,EACrB;AACA,SAAO,6BAA6B,QAAQ,EAAE,MAAM,OAAO,CAAC;AAC9D;AACA,SAAS,kBAAkB,OAAO;AAAA,EAChC,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,SAAO,mBAAmB,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC,KAAK;AAC3D;AACA,SAAS,iBAAiB,OAAO;AAAA,EAC/B,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,SAAO,2BAA2B,oBAAoB,OAAO,EAAE,MAAM,OAAO,CAAC;AAC/E;AACA,SAAS,iBAAiB,OAAO;AAAA,EAC/B,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,MAAI;AACF,WAAO,iBAAiB,OAAO,EAAE,MAAM,OAAO,CAAC;AAAA,EACjD,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,OAAO;AAAA,EAC9B,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,SAAO,oBAAoB,MAAM,mBAAmB,OAAO,EAAE,MAAM,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI;AACzF;AACA,SAAS,gBAAgB,OAAO;AAAA,EAC9B,OAAO;AAAA,EACP,OAAO;AAAA,EACP,SAAS;AACX,IAAI,CAAC,GAAG;AACN,MAAI;AACF,WAAO,gBAAgB,OAAO,EAAE,MAAM,MAAM,OAAO,CAAC;AAAA,EACtD,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,OAAO,EAAE,OAAO,kBAAkB,OAAO,oBAAoB,IAAI,CAAC,GAAG;AAC5F,SAAO,oBAAoB,MAAM,UAAU,OAAO,IAAI,KAAK,IAAI;AACjE;AACA,SAAS,gBAAgB,OAAO,EAAE,OAAO,kBAAkB,OAAO,oBAAoB,IAAI,CAAC,GAAG;AAC5F,MAAI;AACF,WAAO,gBAAgB,OAAO,EAAE,MAAM,KAAK,CAAC;AAAA,EAC9C,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAAS,gBAAgB,OAAO,QAAQ,UAAU,EAAE,MAAM,oBAAoB,GAAG;AAC/E,iBAAe,MAAM;AACrB,YAAU,OAAO,QAAQ,MAAM,OAAO,SAAS,GAAG,OAAO;AAC3D;AACA,SAASA,eAAc,OAAO,EAAE,OAAO,kBAAkB,SAAS,mBAAmB,IAAI,CAAC,GAAG;AAC3F,SAAO,cAAgB,cAAc,KAAK,GAAG,EAAE,MAAM,OAAO,CAAC;AAC/D;AACA,SAAS,cAAc,OAAO,EAAE,OAAO,kBAAkB,SAAS,mBAAmB,IAAI,CAAC,GAAG;AAC3F,MAAI;AACF,WAAOA,eAAc,OAAO,EAAE,MAAM,OAAO,CAAC;AAAA,EAC9C,QAAQ;AACN,WAAO;AAAA,EACT;AACF;AACA,SAASC,gBAAe,OAAO,EAAE,OAAO,kBAAkB,OAAO,SAAS,IAAI,CAAC,GAAG;AAChF,SAAO,eAAiB,cAAc,KAAK,GAAG,EAAE,MAAM,KAAK,CAAC;AAC9D;AACA,SAAS,eAAe,OAAO,EAAE,OAAO,kBAAkB,OAAO,SAAS,IAAI,CAAC,GAAG;AAChF,MAAI;AACF,WAAOA,gBAAe,OAAO,EAAE,MAAM,KAAK,CAAC;AAAA,EAC7C,QAAQ;AACN,WAAO;AAAA,EACT;AACF;", "names": ["getPathLocale", "getQueryLocale"]}