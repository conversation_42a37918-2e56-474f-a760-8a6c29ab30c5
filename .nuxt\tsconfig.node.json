{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "@nuxt/devtools": ["../node_modules/@nuxt/devtools"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "vue-i18n": ["../node_modules/vue-i18n"], "@intlify/shared": ["../node_modules/@intlify/shared"], "@intlify/message-compiler": ["../node_modules/@intlify/message-compiler"], "@intlify/core-base": ["../node_modules/@intlify/core-base"], "@intlify/core": ["../node_modules/@intlify/core"], "@intlify/utils/h3": ["../node_modules/@intlify/utils"], "ufo": ["../node_modules/ufo"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": true, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["./nuxt.node.d.ts", "../modules/*.*", "../nuxt.config.*", "../.config/nuxt.*", "../layers/*/nuxt.config.*", "../layers/*/.config/nuxt.*", "../layers/*/modules/**/*", "../nuxt.schema.*", "../layers/*/nuxt.schema.*"], "exclude": ["../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxt/icon/node_modules", "../node_modules/@nuxt/fonts/node_modules", "../node_modules/@nuxtjs/color-mode/node_modules", "../node_modules/@nuxt/ui/node_modules", "../node_modules/@nuxt/ui-pro/node_modules", "../node_modules/@nuxtjs/i18n/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../dist", "../.data", "../app/**/*", "../modules/*/runtime/**/*", "../layers/*/app/**/*", "../layers/*/modules/*/runtime/**/*", "../modules/*/runtime/server/**/*", "../layers/*/server/**/*", "../layers/*/modules/*/runtime/server/**/*", "../node_modules/runtime", "../node_modules/dist/runtime"]}