"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0;var _default = exports.default = defineNuxtConfig({
  compatibilityDate: '2025-08-30',

  // Modules
  modules: [
  '@nuxt/ui-pro',
  '@nuxtjs/i18n' // สำหรับ Multi-language
  ],

  // CSS Files
  css: ['~/assets/css/main.css'],

  // Nuxt UI Pro Configuration
  uiPro: {
    license: process.env.NUXT_UI_PRO_LICENSE
  },

  // i18n Configuration
  i18n: {
    locales: [
    { code: 'th', name: 'ไทย' },
    { code: 'en', name: 'English' }],

    defaultLocale: 'th',
    strategy: 'no_prefix'
  },

  // Color Mode & Fonts (Auto-registered)
  ui: {
    colorMode: true, // เปิดใช้ Dark/Light mode
    fonts: true // เปิดใช้ Font optimization
  },

  // Development Server
  devtools: { enabled: true },

  // TypeScript Configuration
  typescript: {
    strict: true
  }
}); /* v9-b2aa0af324f0b085 */
