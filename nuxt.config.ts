export default defineNuxtConfig({
  // เปิดใช้งาน Compatibility Version 4
  compatibilityVersion: 4,
  compatibilityDate: '2025-08-30',
  
  // Modules
  modules: [
    '@nuxt/ui-pro',
    '@nuxt/content', // สำหรับ Content management (ถ้าต้องการ)
    '@nuxtjs/i18n'   // สำหรับ Multi-language (ถ้าต้องการ)
  ],
  
  // CSS Files
  css: ['~/assets/css/main.css'],
  
  // Nuxt UI Pro Configuration
  uiPro: {
    license: process.env.NUXT_UI_PRO_LICENSE
  },

  // i18n Configuration
  i18n: {
    locales: [
      { code: 'th', name: 'ไทย', file: 'th.json' },
      { code: 'en', name: 'English', file: 'en.json' }
    ],
    defaultLocale: 'th',
    langDir: 'locales/',
    strategy: 'no_prefix'
  },
  
  // Color Mode & Fonts (Auto-registered)
  ui: {
    colorMode: true,  // เปิดใช้ Dark/Light mode
    fonts: true       // เปิดใช้ Font optimization
  },
  
  // Content Configuration (ถ้าใช้ @nuxt/content)
  content: {
    documentDriven: true,
    highlight: {
      theme: {
        default: 'github-light',
        dark: 'github-dark'
      }
    }
  },
  
  // i18n Configuration (ถ้าใช้ Multi-language)
  i18n: {
    locales: [
      { code: 'en', name: 'English' },
      { code: 'th', name: 'ไทย' }
    ],
    defaultLocale: 'th'
  },
  
  // Development Server
  devtools: { enabled: true },
  
  // TypeScript Configuration
  typescript: {
    strict: true
  }
})