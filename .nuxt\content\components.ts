export const CustomCard = () => import('./../../app/components/CustomCard.vue')
export const UAccordion = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Accordion.vue')
export const UAlert = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Alert.vue')
export const UApp = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/App.vue')
export const UAvatar = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Avatar.vue')
export const UAvatarGroup = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/AvatarGroup.vue')
export const UBadge = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Badge.vue')
export const UBreadcrumb = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Breadcrumb.vue')
export const UButton = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Button.vue')
export const UButtonGroup = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/ButtonGroup.vue')
export const UCalendar = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Calendar.vue')
export const UCard = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Card.vue')
export const UCarousel = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Carousel.vue')
export const UCheckbox = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Checkbox.vue')
export const UCheckboxGroup = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/CheckboxGroup.vue')
export const UChip = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Chip.vue')
export const UCollapsible = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Collapsible.vue')
export const UColorPicker = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/ColorPicker.vue')
export const UCommandPalette = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/CommandPalette.vue')
export const UContainer = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Container.vue')
export const UContextMenu = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/ContextMenu.vue')
export const UContextMenuContent = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/ContextMenuContent.vue')
export const UDrawer = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Drawer.vue')
export const UDropdownMenu = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/DropdownMenu.vue')
export const UDropdownMenuContent = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/DropdownMenuContent.vue')
export const UFileUpload = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/FileUpload.vue')
export const UForm = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Form.vue')
export const UFormField = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/FormField.vue')
export const UIcon = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Icon.vue')
export const UInput = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Input.vue')
export const UInputMenu = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/InputMenu.vue')
export const UInputNumber = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/InputNumber.vue')
export const UInputTags = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/InputTags.vue')
export const UKbd = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Kbd.vue')
export const ULink = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Link.vue')
export const ULinkBase = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/LinkBase.vue')
export const UModal = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Modal.vue')
export const UNavigationMenu = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/NavigationMenu.vue')
export const UOverlayProvider = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/OverlayProvider.vue')
export const UPagination = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Pagination.vue')
export const UPinInput = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/PinInput.vue')
export const UPopover = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Popover.vue')
export const UProgress = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Progress.vue')
export const URadioGroup = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/RadioGroup.vue')
export const USelect = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Select.vue')
export const USelectMenu = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/SelectMenu.vue')
export const USeparator = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Separator.vue')
export const USkeleton = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Skeleton.vue')
export const USlideover = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Slideover.vue')
export const USlider = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Slider.vue')
export const UStepper = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Stepper.vue')
export const USwitch = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Switch.vue')
export const UTable = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Table.vue')
export const UTabs = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Tabs.vue')
export const UTextarea = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Textarea.vue')
export const UTimeline = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Timeline.vue')
export const UToast = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Toast.vue')
export const UToaster = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Toaster.vue')
export const UTooltip = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Tooltip.vue')
export const UTree = () => import('./../../node_modules/@nuxt/ui/dist/runtime/components/Tree.vue')
export const UContentNavigation = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/content/ContentNavigation.vue')
export const UContentSearch = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/content/ContentSearch.vue')
export const UContentSearchButton = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/content/ContentSearchButton.vue')
export const UContentSurround = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/content/ContentSurround.vue')
export const UContentToc = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/content/ContentToc.vue')
export const UColorModeAvatar = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/color-mode/ColorModeAvatar.vue')
export const UColorModeButton = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/color-mode/ColorModeButton.vue')
export const UColorModeImage = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/color-mode/ColorModeImage.vue')
export const UColorModeSelect = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/color-mode/ColorModeSelect.vue')
export const UColorModeSwitch = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/color-mode/ColorModeSwitch.vue')
export const UAuthForm = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/AuthForm.vue')
export const UBanner = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/Banner.vue')
export const UBlogPost = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/BlogPost.vue')
export const UBlogPosts = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/BlogPosts.vue')
export const UChangelogVersion = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChangelogVersion.vue')
export const UChangelogVersions = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChangelogVersions.vue')
export const UChatMessage = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChatMessage.vue')
export const UChatMessages = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChatMessages.vue')
export const UChatPalette = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChatPalette.vue')
export const UChatPrompt = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChatPrompt.vue')
export const UChatPromptSubmit = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/ChatPromptSubmit.vue')
export const UDashboardGroup = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardGroup.vue')
export const UDashboardNavbar = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardNavbar.vue')
export const UDashboardPanel = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardPanel.vue')
export const UDashboardResizeHandle = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardResizeHandle.vue')
export const UDashboardSearch = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardSearch.vue')
export const UDashboardSearchButton = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardSearchButton.vue')
export const UDashboardSidebar = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardSidebar.vue')
export const UDashboardSidebarCollapse = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardSidebarCollapse.vue')
export const UDashboardSidebarToggle = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardSidebarToggle.vue')
export const UDashboardToolbar = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/DashboardToolbar.vue')
export const UError = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/Error.vue')
export const UFooter = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/Footer.vue')
export const UFooterColumns = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/FooterColumns.vue')
export const UHeader = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/Header.vue')
export const UMain = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/Main.vue')
export const UPage = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/Page.vue')
export const UPageAccordion = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageAccordion.vue')
export const UPageAnchors = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageAnchors.vue')
export const UPageAside = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageAside.vue')
export const UPageBody = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageBody.vue')
export const UPageCTA = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageCTA.vue')
export const UPageCard = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageCard.vue')
export const UPageColumns = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageColumns.vue')
export const UPageFeature = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageFeature.vue')
export const UPageGrid = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageGrid.vue')
export const UPageHeader = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageHeader.vue')
export const UPageHero = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageHero.vue')
export const UPageLinks = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageLinks.vue')
export const UPageList = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageList.vue')
export const UPageLogos = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageLogos.vue')
export const UPageMarquee = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageMarquee.vue')
export const UPageSection = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PageSection.vue')
export const UPricingPlan = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PricingPlan.vue')
export const UPricingPlans = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PricingPlans.vue')
export const UPricingTable = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/PricingTable.vue')
export const UUser = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/User.vue')
export const ULocaleSelect = () => import('./../../node_modules/@nuxt/ui-pro/dist/runtime/components/locale/LocaleSelect.vue')
export const NuxtWelcome = () => import('./../../node_modules/nuxt/dist/app/components/welcome.vue')
export const NuxtLayout = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-layout')
export const NuxtErrorBoundary = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-error-boundary.vue')
export const ClientOnly = () => import('./../../node_modules/nuxt/dist/app/components/client-only')
export const DevOnly = () => import('./../../node_modules/nuxt/dist/app/components/dev-only')
export const ServerPlaceholder = () => import('./../../node_modules/nuxt/dist/app/components/server-placeholder')
export const NuxtLink = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-link')
export const NuxtLoadingIndicator = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-loading-indicator')
export const NuxtTime = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-time.vue')
export const NuxtRouteAnnouncer = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-route-announcer')
export const NuxtImg = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-stubs')
export const NuxtPicture = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-stubs')
export const ColorScheme = () => import('./../../node_modules/@nuxtjs/color-mode/dist/runtime/component.vue3.vue')
export const ContentRenderer = () => import('./../../node_modules/@nuxt/content/dist/runtime/components/ContentRenderer.vue')
export const MDC = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDC.vue')
export const MDCCached = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDCCached.vue')
export const MDCRenderer = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDCRenderer.vue')
export const MDCSlot = () => import('./../../node_modules/@nuxtjs/mdc/dist/runtime/components/MDCSlot.vue')
export const NuxtLinkLocale = () => import('./../../node_modules/@nuxtjs/i18n/dist/runtime/components/NuxtLinkLocale')
export const SwitchLocalePathLink = () => import('./../../node_modules/@nuxtjs/i18n/dist/runtime/components/SwitchLocalePathLink')
export const NuxtPage = () => import('./../../node_modules/nuxt/dist/pages/runtime/page')
export const NoScript = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Link = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Base = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Title = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Meta = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Style = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Head = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Html = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const Body = () => import('./../../node_modules/nuxt/dist/head/runtime/components')
export const NuxtIsland = () => import('./../../node_modules/nuxt/dist/app/components/nuxt-island')
export const globalComponents: string[] = ["ProseA","ProseAccordion","ProseAccordionItem","ProseBadge","ProseBlockquote","ProseCallout","ProseCard","ProseCardGroup","ProseCode","ProseCodeCollapse","ProseCodeGroup","ProseCodeIcon","ProseCodePreview","ProseCodeTree","ProseCollapsible","ProseEm","ProseField","ProseFieldGroup","ProseH1","ProseH2","ProseH3","ProseH4","ProseHr","ProseIcon","ProseImg","ProseKbd","ProseLi","ProseOl","ProseP","ProsePre","ProseScript","ProseSteps","ProseStrong","ProseTable","ProseTabs","ProseTabsItem","ProseTbody","ProseTd","ProseTh","ProseThead","ProseTr","ProseUl","ProseCaution","ProseNote","ProseTip","ProseWarning","ProseH5","ProseH6","Icon"]
export const localComponents: string[] = ["CustomCard","UAccordion","UAlert","UApp","UAvatar","UAvatarGroup","UBadge","UBreadcrumb","UButton","UButtonGroup","UCalendar","UCard","UCarousel","UCheckbox","UCheckboxGroup","UChip","UCollapsible","UColorPicker","UCommandPalette","UContainer","UContextMenu","UContextMenuContent","UDrawer","UDropdownMenu","UDropdownMenuContent","UFileUpload","UForm","UFormField","UIcon","UInput","UInputMenu","UInputNumber","UInputTags","UKbd","ULink","ULinkBase","UModal","UNavigationMenu","UOverlayProvider","UPagination","UPinInput","UPopover","UProgress","URadioGroup","USelect","USelectMenu","USeparator","USkeleton","USlideover","USlider","UStepper","USwitch","UTable","UTabs","UTextarea","UTimeline","UToast","UToaster","UTooltip","UTree","UContentNavigation","UContentSearch","UContentSearchButton","UContentSurround","UContentToc","UColorModeAvatar","UColorModeButton","UColorModeImage","UColorModeSelect","UColorModeSwitch","UAuthForm","UBanner","UBlogPost","UBlogPosts","UChangelogVersion","UChangelogVersions","UChatMessage","UChatMessages","UChatPalette","UChatPrompt","UChatPromptSubmit","UDashboardGroup","UDashboardNavbar","UDashboardPanel","UDashboardResizeHandle","UDashboardSearch","UDashboardSearchButton","UDashboardSidebar","UDashboardSidebarCollapse","UDashboardSidebarToggle","UDashboardToolbar","UError","UFooter","UFooterColumns","UHeader","UMain","UPage","UPageAccordion","UPageAnchors","UPageAside","UPageBody","UPageCTA","UPageCard","UPageColumns","UPageFeature","UPageGrid","UPageHeader","UPageHero","UPageLinks","UPageList","UPageLogos","UPageMarquee","UPageSection","UPricingPlan","UPricingPlans","UPricingTable","UUser","ULocaleSelect","NuxtWelcome","NuxtLayout","NuxtErrorBoundary","ClientOnly","DevOnly","ServerPlaceholder","NuxtLink","NuxtLoadingIndicator","NuxtTime","NuxtRouteAnnouncer","NuxtImg","NuxtPicture","ColorScheme","ContentRenderer","MDC","MDCCached","MDCRenderer","MDCSlot","NuxtLinkLocale","SwitchLocalePathLink","NuxtPage","NoScript","Link","Base","Title","Meta","Style","Head","Html","Body","NuxtIsland"]