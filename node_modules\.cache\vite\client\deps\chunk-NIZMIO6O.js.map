{"version": 3, "sources": ["../../../../@intlify/message-compiler/dist/message-compiler.mjs"], "sourcesContent": ["/*!\n  * message-compiler v11.1.11\n  * (c) 2025 ka<PERSON><PERSON> ka<PERSON>\n  * Released under the MIT License.\n  */\nimport { format, assign, join, isString } from '@intlify/shared';\n\nconst LOCATION_STUB = {\n    start: { line: 1, column: 1, offset: 0 },\n    end: { line: 1, column: 1, offset: 0 }\n};\nfunction createPosition(line, column, offset) {\n    return { line, column, offset };\n}\nfunction createLocation(start, end, source) {\n    const loc = { start, end };\n    if (source != null) {\n        loc.source = source;\n    }\n    return loc;\n}\n\nconst CompileErrorCodes = {\n    // tokenizer error codes\n    EXPECTED_TOKEN: 1,\n    INVALID_TOKEN_IN_PLACEHOLDER: 2,\n    UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,\n    UNKNOWN_ESCAPE_SEQUENCE: 4,\n    INVALID_UNICODE_ESCAPE_SEQUENCE: 5,\n    UNBALANCED_CLOSING_BRACE: 6,\n    UNTERMINATED_CLOSING_BRACE: 7,\n    EMPTY_PLACEHOLDER: 8,\n    NOT_ALLOW_NEST_PLACEHOLDER: 9,\n    INVALID_LINKED_FORMAT: 10,\n    // parser error codes\n    MUST_HAVE_MESSAGES_IN_PLURAL: 11,\n    UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,\n    UNEXPECTED_EMPTY_LINKED_KEY: 13,\n    UNEXPECTED_LEXICAL_ANALYSIS: 14,\n    // generator error codes\n    UNHANDLED_CODEGEN_NODE_TYPE: 15,\n    // minifier error codes\n    UNHANDLED_MINIFIER_NODE_TYPE: 16\n};\n// Special value for higher-order compilers to pick up the last code\n// to avoid collision of error codes.\n// This should always be kept as the last item.\nconst COMPILE_ERROR_CODES_EXTEND_POINT = 17;\n/** @internal */\nconst errorMessages = {\n    // tokenizer error messages\n    [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,\n    [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,\n    [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,\n    [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\\\{0}`,\n    [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,\n    [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,\n    [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,\n    [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,\n    [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,\n    [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,\n    // parser error messages\n    [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,\n    [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,\n    [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,\n    // generator error messages\n    [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,\n    // minimizer error messages\n    [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`\n};\nfunction createCompileError(code, loc, options = {}) {\n    const { domain, messages, args } = options;\n    const msg = (process.env.NODE_ENV !== 'production')\n        ? format((messages || errorMessages)[code] || '', ...(args || []))\n        : code;\n    const error = new SyntaxError(String(msg));\n    error.code = code;\n    if (loc) {\n        error.location = loc;\n    }\n    error.domain = domain;\n    return error;\n}\n/** @internal */\nfunction defaultOnError(error) {\n    throw error;\n}\n\n// eslint-disable-next-line no-useless-escape\nconst RE_HTML_TAG = /<\\/?[\\w\\s=\"/.':;#-\\/]+>/;\nconst detectHtmlTag = (source) => RE_HTML_TAG.test(source);\n\nconst CHAR_SP = ' ';\nconst CHAR_CR = '\\r';\nconst CHAR_LF = '\\n';\nconst CHAR_LS = String.fromCharCode(0x2028);\nconst CHAR_PS = String.fromCharCode(0x2029);\nfunction createScanner(str) {\n    const _buf = str;\n    let _index = 0;\n    let _line = 1;\n    let _column = 1;\n    let _peekOffset = 0;\n    const isCRLF = (index) => _buf[index] === CHAR_CR && _buf[index + 1] === CHAR_LF;\n    const isLF = (index) => _buf[index] === CHAR_LF;\n    const isPS = (index) => _buf[index] === CHAR_PS;\n    const isLS = (index) => _buf[index] === CHAR_LS;\n    const isLineEnd = (index) => isCRLF(index) || isLF(index) || isPS(index) || isLS(index);\n    const index = () => _index;\n    const line = () => _line;\n    const column = () => _column;\n    const peekOffset = () => _peekOffset;\n    const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];\n    const currentChar = () => charAt(_index);\n    const currentPeek = () => charAt(_index + _peekOffset);\n    function next() {\n        _peekOffset = 0;\n        if (isLineEnd(_index)) {\n            _line++;\n            _column = 0;\n        }\n        if (isCRLF(_index)) {\n            _index++;\n        }\n        _index++;\n        _column++;\n        return _buf[_index];\n    }\n    function peek() {\n        if (isCRLF(_index + _peekOffset)) {\n            _peekOffset++;\n        }\n        _peekOffset++;\n        return _buf[_index + _peekOffset];\n    }\n    function reset() {\n        _index = 0;\n        _line = 1;\n        _column = 1;\n        _peekOffset = 0;\n    }\n    function resetPeek(offset = 0) {\n        _peekOffset = offset;\n    }\n    function skipToPeek() {\n        const target = _index + _peekOffset;\n        while (target !== _index) {\n            next();\n        }\n        _peekOffset = 0;\n    }\n    return {\n        index,\n        line,\n        column,\n        peekOffset,\n        charAt,\n        currentChar,\n        currentPeek,\n        next,\n        peek,\n        reset,\n        resetPeek,\n        skipToPeek\n    };\n}\n\nconst EOF = undefined;\nconst DOT = '.';\nconst LITERAL_DELIMITER = \"'\";\nconst ERROR_DOMAIN$3 = 'tokenizer';\nfunction createTokenizer(source, options = {}) {\n    const location = options.location !== false;\n    const _scnr = createScanner(source);\n    const currentOffset = () => _scnr.index();\n    const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());\n    const _initLoc = currentPosition();\n    const _initOffset = currentOffset();\n    const _context = {\n        currentType: 13 /* TokenTypes.EOF */,\n        offset: _initOffset,\n        startLoc: _initLoc,\n        endLoc: _initLoc,\n        lastType: 13 /* TokenTypes.EOF */,\n        lastOffset: _initOffset,\n        lastStartLoc: _initLoc,\n        lastEndLoc: _initLoc,\n        braceNest: 0,\n        inLinked: false,\n        text: ''\n    };\n    const context = () => _context;\n    const { onError } = options;\n    function emitError(code, pos, offset, ...args) {\n        const ctx = context();\n        pos.column += offset;\n        pos.offset += offset;\n        if (onError) {\n            const loc = location ? createLocation(ctx.startLoc, pos) : null;\n            const err = createCompileError(code, loc, {\n                domain: ERROR_DOMAIN$3,\n                args\n            });\n            onError(err);\n        }\n    }\n    function getToken(context, type, value) {\n        context.endLoc = currentPosition();\n        context.currentType = type;\n        const token = { type };\n        if (location) {\n            token.loc = createLocation(context.startLoc, context.endLoc);\n        }\n        if (value != null) {\n            token.value = value;\n        }\n        return token;\n    }\n    const getEndToken = (context) => getToken(context, 13 /* TokenTypes.EOF */);\n    function eat(scnr, ch) {\n        if (scnr.currentChar() === ch) {\n            scnr.next();\n            return ch;\n        }\n        else {\n            emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);\n            return '';\n        }\n    }\n    function peekSpaces(scnr) {\n        let buf = '';\n        while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {\n            buf += scnr.currentPeek();\n            scnr.peek();\n        }\n        return buf;\n    }\n    function skipSpaces(scnr) {\n        const buf = peekSpaces(scnr);\n        scnr.skipToPeek();\n        return buf;\n    }\n    function isIdentifierStart(ch) {\n        if (ch === EOF) {\n            return false;\n        }\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            cc === 95 // _\n        );\n    }\n    function isNumberStart(ch) {\n        if (ch === EOF) {\n            return false;\n        }\n        const cc = ch.charCodeAt(0);\n        return cc >= 48 && cc <= 57; // 0-9\n    }\n    function isNamedIdentifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = isIdentifierStart(scnr.currentPeek());\n        scnr.resetPeek();\n        return ret;\n    }\n    function isListIdentifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ch = scnr.currentPeek() === '-' ? scnr.peek() : scnr.currentPeek();\n        const ret = isNumberStart(ch);\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLiteralStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 2 /* TokenTypes.BraceLeft */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === LITERAL_DELIMITER;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedDotStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 7 /* TokenTypes.LinkedAlias */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \".\" /* TokenChars.LinkedDot */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedModifierStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 8 /* TokenTypes.LinkedDot */) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = isIdentifierStart(scnr.currentPeek());\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedDelimiterStart(scnr, context) {\n        const { currentType } = context;\n        if (!(currentType === 7 /* TokenTypes.LinkedAlias */ ||\n            currentType === 11 /* TokenTypes.LinkedModifier */)) {\n            return false;\n        }\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \":\" /* TokenChars.LinkedDelimiter */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isLinkedReferStart(scnr, context) {\n        const { currentType } = context;\n        if (currentType !== 9 /* TokenTypes.LinkedDelimiter */) {\n            return false;\n        }\n        const fn = () => {\n            const ch = scnr.currentPeek();\n            if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                return isIdentifierStart(scnr.peek());\n            }\n            else if (ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                ch === \":\" /* TokenChars.LinkedDelimiter */ ||\n                ch === \".\" /* TokenChars.LinkedDot */ ||\n                ch === CHAR_SP ||\n                !ch) {\n                return false;\n            }\n            else if (ch === CHAR_LF) {\n                scnr.peek();\n                return fn();\n            }\n            else {\n                // other characters\n                return isTextStart(scnr, false);\n            }\n        };\n        const ret = fn();\n        scnr.resetPeek();\n        return ret;\n    }\n    function isPluralStart(scnr) {\n        peekSpaces(scnr);\n        const ret = scnr.currentPeek() === \"|\" /* TokenChars.Pipe */;\n        scnr.resetPeek();\n        return ret;\n    }\n    function isTextStart(scnr, reset = true) {\n        const fn = (hasSpace = false, prev = '') => {\n            const ch = scnr.currentPeek();\n            if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                return hasSpace;\n            }\n            else if (ch === \"@\" /* TokenChars.LinkedAlias */ || !ch) {\n                return hasSpace;\n            }\n            else if (ch === \"|\" /* TokenChars.Pipe */) {\n                return !(prev === CHAR_SP || prev === CHAR_LF);\n            }\n            else if (ch === CHAR_SP) {\n                scnr.peek();\n                return fn(true, CHAR_SP);\n            }\n            else if (ch === CHAR_LF) {\n                scnr.peek();\n                return fn(true, CHAR_LF);\n            }\n            else {\n                return true;\n            }\n        };\n        const ret = fn();\n        reset && scnr.resetPeek();\n        return ret;\n    }\n    function takeChar(scnr, fn) {\n        const ch = scnr.currentChar();\n        if (ch === EOF) {\n            return EOF;\n        }\n        if (fn(ch)) {\n            scnr.next();\n            return ch;\n        }\n        return null;\n    }\n    function isIdentifier(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            (cc >= 48 && cc <= 57) || // 0-9\n            cc === 95 || // _\n            cc === 36 // $\n        );\n    }\n    function takeIdentifierChar(scnr) {\n        return takeChar(scnr, isIdentifier);\n    }\n    function isNamedIdentifier(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 97 && cc <= 122) || // a-z\n            (cc >= 65 && cc <= 90) || // A-Z\n            (cc >= 48 && cc <= 57) || // 0-9\n            cc === 95 || // _\n            cc === 36 || // $\n            cc === 45 // -\n        );\n    }\n    function takeNamedIdentifierChar(scnr) {\n        return takeChar(scnr, isNamedIdentifier);\n    }\n    function isDigit(ch) {\n        const cc = ch.charCodeAt(0);\n        return cc >= 48 && cc <= 57; // 0-9\n    }\n    function takeDigit(scnr) {\n        return takeChar(scnr, isDigit);\n    }\n    function isHexDigit(ch) {\n        const cc = ch.charCodeAt(0);\n        return ((cc >= 48 && cc <= 57) || // 0-9\n            (cc >= 65 && cc <= 70) || // A-F\n            (cc >= 97 && cc <= 102)); // a-f\n    }\n    function takeHexDigit(scnr) {\n        return takeChar(scnr, isHexDigit);\n    }\n    function getDigits(scnr) {\n        let ch = '';\n        let num = '';\n        while ((ch = takeDigit(scnr))) {\n            num += ch;\n        }\n        return num;\n    }\n    function readText(scnr) {\n        let buf = '';\n        while (true) {\n            const ch = scnr.currentChar();\n            if (ch === \"{\" /* TokenChars.BraceLeft */ ||\n                ch === \"}\" /* TokenChars.BraceRight */ ||\n                ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                !ch) {\n                break;\n            }\n            else if (ch === CHAR_SP || ch === CHAR_LF) {\n                if (isTextStart(scnr)) {\n                    buf += ch;\n                    scnr.next();\n                }\n                else if (isPluralStart(scnr)) {\n                    break;\n                }\n                else {\n                    buf += ch;\n                    scnr.next();\n                }\n            }\n            else {\n                buf += ch;\n                scnr.next();\n            }\n        }\n        return buf;\n    }\n    function readNamedIdentifier(scnr) {\n        skipSpaces(scnr);\n        let ch = '';\n        let name = '';\n        while ((ch = takeNamedIdentifierChar(scnr))) {\n            name += ch;\n        }\n        if (scnr.currentChar() === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        return name;\n    }\n    function readListIdentifier(scnr) {\n        skipSpaces(scnr);\n        let value = '';\n        if (scnr.currentChar() === '-') {\n            scnr.next();\n            value += `-${getDigits(scnr)}`;\n        }\n        else {\n            value += getDigits(scnr);\n        }\n        if (scnr.currentChar() === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n        }\n        return value;\n    }\n    function isLiteral(ch) {\n        return ch !== LITERAL_DELIMITER && ch !== CHAR_LF;\n    }\n    function readLiteral(scnr) {\n        skipSpaces(scnr);\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n        let ch = '';\n        let literal = '';\n        while ((ch = takeChar(scnr, isLiteral))) {\n            if (ch === '\\\\') {\n                literal += readEscapeSequence(scnr);\n            }\n            else {\n                literal += ch;\n            }\n        }\n        const current = scnr.currentChar();\n        if (current === CHAR_LF || current === EOF) {\n            emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);\n            // TODO: Is it correct really?\n            if (current === CHAR_LF) {\n                scnr.next();\n                // eslint-disable-next-line no-useless-escape\n                eat(scnr, `\\'`);\n            }\n            return literal;\n        }\n        // eslint-disable-next-line no-useless-escape\n        eat(scnr, `\\'`);\n        return literal;\n    }\n    function readEscapeSequence(scnr) {\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case '\\\\':\n            case `\\'`: // eslint-disable-line no-useless-escape\n                scnr.next();\n                return `\\\\${ch}`;\n            case 'u':\n                return readUnicodeEscapeSequence(scnr, ch, 4);\n            case 'U':\n                return readUnicodeEscapeSequence(scnr, ch, 6);\n            default:\n                emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);\n                return '';\n        }\n    }\n    function readUnicodeEscapeSequence(scnr, unicode, digits) {\n        eat(scnr, unicode);\n        let sequence = '';\n        for (let i = 0; i < digits; i++) {\n            const ch = takeHexDigit(scnr);\n            if (!ch) {\n                emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\\\${unicode}${sequence}${scnr.currentChar()}`);\n                break;\n            }\n            sequence += ch;\n        }\n        return `\\\\${unicode}${sequence}`;\n    }\n    function isInvalidIdentifier(ch) {\n        return (ch !== \"{\" /* TokenChars.BraceLeft */ &&\n            ch !== \"}\" /* TokenChars.BraceRight */ &&\n            ch !== CHAR_SP &&\n            ch !== CHAR_LF);\n    }\n    function readInvalidIdentifier(scnr) {\n        skipSpaces(scnr);\n        let ch = '';\n        let identifiers = '';\n        while ((ch = takeChar(scnr, isInvalidIdentifier))) {\n            identifiers += ch;\n        }\n        return identifiers;\n    }\n    function readLinkedModifier(scnr) {\n        let ch = '';\n        let name = '';\n        while ((ch = takeIdentifierChar(scnr))) {\n            name += ch;\n        }\n        return name;\n    }\n    function readLinkedRefer(scnr) {\n        const fn = (buf) => {\n            const ch = scnr.currentChar();\n            if (ch === \"{\" /* TokenChars.BraceLeft */ ||\n                ch === \"@\" /* TokenChars.LinkedAlias */ ||\n                ch === \"|\" /* TokenChars.Pipe */ ||\n                ch === \"(\" /* TokenChars.ParenLeft */ ||\n                ch === \")\" /* TokenChars.ParenRight */ ||\n                !ch) {\n                return buf;\n            }\n            else if (ch === CHAR_SP) {\n                return buf;\n            }\n            else if (ch === CHAR_LF || ch === DOT) {\n                buf += ch;\n                scnr.next();\n                return fn(buf);\n            }\n            else {\n                buf += ch;\n                scnr.next();\n                return fn(buf);\n            }\n        };\n        return fn('');\n    }\n    function readPlural(scnr) {\n        skipSpaces(scnr);\n        const plural = eat(scnr, \"|\" /* TokenChars.Pipe */);\n        skipSpaces(scnr);\n        return plural;\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readTokenInPlaceholder(scnr, context) {\n        let token = null;\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case \"{\" /* TokenChars.BraceLeft */:\n                if (context.braceNest >= 1) {\n                    emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);\n                }\n                scnr.next();\n                token = getToken(context, 2 /* TokenTypes.BraceLeft */, \"{\" /* TokenChars.BraceLeft */);\n                skipSpaces(scnr);\n                context.braceNest++;\n                return token;\n            case \"}\" /* TokenChars.BraceRight */:\n                if (context.braceNest > 0 &&\n                    context.currentType === 2 /* TokenTypes.BraceLeft */) {\n                    emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);\n                }\n                scnr.next();\n                token = getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n                context.braceNest--;\n                context.braceNest > 0 && skipSpaces(scnr);\n                if (context.inLinked && context.braceNest === 0) {\n                    context.inLinked = false;\n                }\n                return token;\n            case \"@\" /* TokenChars.LinkedAlias */:\n                if (context.braceNest > 0) {\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                }\n                token = readTokenInLinked(scnr, context) || getEndToken(context);\n                context.braceNest = 0;\n                return token;\n            default: {\n                let validNamedIdentifier = true;\n                let validListIdentifier = true;\n                let validLiteral = true;\n                if (isPluralStart(scnr)) {\n                    if (context.braceNest > 0) {\n                        emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                    }\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (context.braceNest > 0 &&\n                    (context.currentType === 4 /* TokenTypes.Named */ ||\n                        context.currentType === 5 /* TokenTypes.List */ ||\n                        context.currentType === 6 /* TokenTypes.Literal */)) {\n                    emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);\n                    context.braceNest = 0;\n                    return readToken(scnr, context);\n                }\n                if ((validNamedIdentifier = isNamedIdentifierStart(scnr, context))) {\n                    token = getToken(context, 4 /* TokenTypes.Named */, readNamedIdentifier(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if ((validListIdentifier = isListIdentifierStart(scnr, context))) {\n                    token = getToken(context, 5 /* TokenTypes.List */, readListIdentifier(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if ((validLiteral = isLiteralStart(scnr, context))) {\n                    token = getToken(context, 6 /* TokenTypes.Literal */, readLiteral(scnr));\n                    skipSpaces(scnr);\n                    return token;\n                }\n                if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {\n                    // TODO: we should be re-designed invalid cases, when we will extend message syntax near the future ...\n                    token = getToken(context, 12 /* TokenTypes.InvalidPlace */, readInvalidIdentifier(scnr));\n                    emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);\n                    skipSpaces(scnr);\n                    return token;\n                }\n                break;\n            }\n        }\n        return token;\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readTokenInLinked(scnr, context) {\n        const { currentType } = context;\n        let token = null;\n        const ch = scnr.currentChar();\n        if ((currentType === 7 /* TokenTypes.LinkedAlias */ ||\n            currentType === 8 /* TokenTypes.LinkedDot */ ||\n            currentType === 11 /* TokenTypes.LinkedModifier */ ||\n            currentType === 9 /* TokenTypes.LinkedDelimiter */) &&\n            (ch === CHAR_LF || ch === CHAR_SP)) {\n            emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n        }\n        switch (ch) {\n            case \"@\" /* TokenChars.LinkedAlias */:\n                scnr.next();\n                token = getToken(context, 7 /* TokenTypes.LinkedAlias */, \"@\" /* TokenChars.LinkedAlias */);\n                context.inLinked = true;\n                return token;\n            case \".\" /* TokenChars.LinkedDot */:\n                skipSpaces(scnr);\n                scnr.next();\n                return getToken(context, 8 /* TokenTypes.LinkedDot */, \".\" /* TokenChars.LinkedDot */);\n            case \":\" /* TokenChars.LinkedDelimiter */:\n                skipSpaces(scnr);\n                scnr.next();\n                return getToken(context, 9 /* TokenTypes.LinkedDelimiter */, \":\" /* TokenChars.LinkedDelimiter */);\n            default:\n                if (isPluralStart(scnr)) {\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (isLinkedDotStart(scnr, context) ||\n                    isLinkedDelimiterStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    return readTokenInLinked(scnr, context);\n                }\n                if (isLinkedModifierStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    return getToken(context, 11 /* TokenTypes.LinkedModifier */, readLinkedModifier(scnr));\n                }\n                if (isLinkedReferStart(scnr, context)) {\n                    skipSpaces(scnr);\n                    if (ch === \"{\" /* TokenChars.BraceLeft */) {\n                        // scan the placeholder\n                        return readTokenInPlaceholder(scnr, context) || token;\n                    }\n                    else {\n                        return getToken(context, 10 /* TokenTypes.LinkedKey */, readLinkedRefer(scnr));\n                    }\n                }\n                if (currentType === 7 /* TokenTypes.LinkedAlias */) {\n                    emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);\n                }\n                context.braceNest = 0;\n                context.inLinked = false;\n                return readToken(scnr, context);\n        }\n    }\n    // TODO: We need refactoring of token parsing ...\n    function readToken(scnr, context) {\n        let token = { type: 13 /* TokenTypes.EOF */ };\n        if (context.braceNest > 0) {\n            return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n        }\n        if (context.inLinked) {\n            return readTokenInLinked(scnr, context) || getEndToken(context);\n        }\n        const ch = scnr.currentChar();\n        switch (ch) {\n            case \"{\" /* TokenChars.BraceLeft */:\n                return readTokenInPlaceholder(scnr, context) || getEndToken(context);\n            case \"}\" /* TokenChars.BraceRight */:\n                emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);\n                scnr.next();\n                return getToken(context, 3 /* TokenTypes.BraceRight */, \"}\" /* TokenChars.BraceRight */);\n            case \"@\" /* TokenChars.LinkedAlias */:\n                return readTokenInLinked(scnr, context) || getEndToken(context);\n            default: {\n                if (isPluralStart(scnr)) {\n                    token = getToken(context, 1 /* TokenTypes.Pipe */, readPlural(scnr));\n                    // reset\n                    context.braceNest = 0;\n                    context.inLinked = false;\n                    return token;\n                }\n                if (isTextStart(scnr)) {\n                    return getToken(context, 0 /* TokenTypes.Text */, readText(scnr));\n                }\n                break;\n            }\n        }\n        return token;\n    }\n    function nextToken() {\n        const { currentType, offset, startLoc, endLoc } = _context;\n        _context.lastType = currentType;\n        _context.lastOffset = offset;\n        _context.lastStartLoc = startLoc;\n        _context.lastEndLoc = endLoc;\n        _context.offset = currentOffset();\n        _context.startLoc = currentPosition();\n        if (_scnr.currentChar() === EOF) {\n            return getToken(_context, 13 /* TokenTypes.EOF */);\n        }\n        return readToken(_scnr, _context);\n    }\n    return {\n        nextToken,\n        currentOffset,\n        currentPosition,\n        context\n    };\n}\n\nconst ERROR_DOMAIN$2 = 'parser';\n// Backslash backslash, backslash quote, uHHHH, UHHHHHH.\nconst KNOWN_ESCAPES = /(?:\\\\\\\\|\\\\'|\\\\u([0-9a-fA-F]{4})|\\\\U([0-9a-fA-F]{6}))/g;\nfunction fromEscapeSequence(match, codePoint4, codePoint6) {\n    switch (match) {\n        case `\\\\\\\\`:\n            return `\\\\`;\n        // eslint-disable-next-line no-useless-escape\n        case `\\\\\\'`:\n            // eslint-disable-next-line no-useless-escape\n            return `\\'`;\n        default: {\n            const codePoint = parseInt(codePoint4 || codePoint6, 16);\n            if (codePoint <= 0xd7ff || codePoint >= 0xe000) {\n                return String.fromCodePoint(codePoint);\n            }\n            // invalid ...\n            // Replace them with U+FFFD REPLACEMENT CHARACTER.\n            return '�';\n        }\n    }\n}\nfunction createParser(options = {}) {\n    const location = options.location !== false;\n    const { onError } = options;\n    function emitError(tokenzer, code, start, offset, ...args) {\n        const end = tokenzer.currentPosition();\n        end.offset += offset;\n        end.column += offset;\n        if (onError) {\n            const loc = location ? createLocation(start, end) : null;\n            const err = createCompileError(code, loc, {\n                domain: ERROR_DOMAIN$2,\n                args\n            });\n            onError(err);\n        }\n    }\n    function startNode(type, offset, loc) {\n        const node = { type };\n        if (location) {\n            node.start = offset;\n            node.end = offset;\n            node.loc = { start: loc, end: loc };\n        }\n        return node;\n    }\n    function endNode(node, offset, pos, type) {\n        if (location) {\n            node.end = offset;\n            if (node.loc) {\n                node.loc.end = pos;\n            }\n        }\n    }\n    function parseText(tokenizer, value) {\n        const context = tokenizer.context();\n        const node = startNode(3 /* NodeTypes.Text */, context.offset, context.startLoc);\n        node.value = value;\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseList(tokenizer, index) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(5 /* NodeTypes.List */, offset, loc);\n        node.index = parseInt(index, 10);\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseNamed(tokenizer, key) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(4 /* NodeTypes.Named */, offset, loc);\n        node.key = key;\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLiteral(tokenizer, value) {\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get brace left loc\n        const node = startNode(9 /* NodeTypes.Literal */, offset, loc);\n        node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);\n        tokenizer.nextToken(); // skip brach right\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLinkedModifier(tokenizer) {\n        const token = tokenizer.nextToken();\n        const context = tokenizer.context();\n        const { lastOffset: offset, lastStartLoc: loc } = context; // get linked dot loc\n        const node = startNode(8 /* NodeTypes.LinkedModifier */, offset, loc);\n        if (token.type !== 11 /* TokenTypes.LinkedModifier */) {\n            // empty modifier\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);\n            node.value = '';\n            endNode(node, offset, loc);\n            return {\n                nextConsumeToken: token,\n                node\n            };\n        }\n        // check token\n        if (token.value == null) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        node.value = token.value || '';\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return {\n            node\n        };\n    }\n    function parseLinkedKey(tokenizer, value) {\n        const context = tokenizer.context();\n        const node = startNode(7 /* NodeTypes.LinkedKey */, context.offset, context.startLoc);\n        node.value = value;\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseLinked(tokenizer) {\n        const context = tokenizer.context();\n        const linkedNode = startNode(6 /* NodeTypes.Linked */, context.offset, context.startLoc);\n        let token = tokenizer.nextToken();\n        if (token.type === 8 /* TokenTypes.LinkedDot */) {\n            const parsed = parseLinkedModifier(tokenizer);\n            linkedNode.modifier = parsed.node;\n            token = parsed.nextConsumeToken || tokenizer.nextToken();\n        }\n        // asset check token\n        if (token.type !== 9 /* TokenTypes.LinkedDelimiter */) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n        }\n        token = tokenizer.nextToken();\n        // skip brace left\n        if (token.type === 2 /* TokenTypes.BraceLeft */) {\n            token = tokenizer.nextToken();\n        }\n        switch (token.type) {\n            case 10 /* TokenTypes.LinkedKey */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseLinkedKey(tokenizer, token.value || '');\n                break;\n            case 4 /* TokenTypes.Named */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseNamed(tokenizer, token.value || '');\n                break;\n            case 5 /* TokenTypes.List */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseList(tokenizer, token.value || '');\n                break;\n            case 6 /* TokenTypes.Literal */:\n                if (token.value == null) {\n                    emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                }\n                linkedNode.key = parseLiteral(tokenizer, token.value || '');\n                break;\n            default: {\n                // empty key\n                emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);\n                const nextContext = tokenizer.context();\n                const emptyLinkedKeyNode = startNode(7 /* NodeTypes.LinkedKey */, nextContext.offset, nextContext.startLoc);\n                emptyLinkedKeyNode.value = '';\n                endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);\n                linkedNode.key = emptyLinkedKeyNode;\n                endNode(linkedNode, nextContext.offset, nextContext.startLoc);\n                return {\n                    nextConsumeToken: token,\n                    node: linkedNode\n                };\n            }\n        }\n        endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return {\n            node: linkedNode\n        };\n    }\n    function parseMessage(tokenizer) {\n        const context = tokenizer.context();\n        const startOffset = context.currentType === 1 /* TokenTypes.Pipe */\n            ? tokenizer.currentOffset()\n            : context.offset;\n        const startLoc = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.endLoc\n            : context.startLoc;\n        const node = startNode(2 /* NodeTypes.Message */, startOffset, startLoc);\n        node.items = [];\n        let nextToken = null;\n        do {\n            const token = nextToken || tokenizer.nextToken();\n            nextToken = null;\n            switch (token.type) {\n                case 0 /* TokenTypes.Text */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseText(tokenizer, token.value || ''));\n                    break;\n                case 5 /* TokenTypes.List */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseList(tokenizer, token.value || ''));\n                    break;\n                case 4 /* TokenTypes.Named */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseNamed(tokenizer, token.value || ''));\n                    break;\n                case 6 /* TokenTypes.Literal */:\n                    if (token.value == null) {\n                        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));\n                    }\n                    node.items.push(parseLiteral(tokenizer, token.value || ''));\n                    break;\n                case 7 /* TokenTypes.LinkedAlias */: {\n                    const parsed = parseLinked(tokenizer);\n                    node.items.push(parsed.node);\n                    nextToken = parsed.nextConsumeToken || null;\n                    break;\n                }\n            }\n        } while (context.currentType !== 13 /* TokenTypes.EOF */ &&\n            context.currentType !== 1 /* TokenTypes.Pipe */);\n        // adjust message node loc\n        const endOffset = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.lastOffset\n            : tokenizer.currentOffset();\n        const endLoc = context.currentType === 1 /* TokenTypes.Pipe */\n            ? context.lastEndLoc\n            : tokenizer.currentPosition();\n        endNode(node, endOffset, endLoc);\n        return node;\n    }\n    function parsePlural(tokenizer, offset, loc, msgNode) {\n        const context = tokenizer.context();\n        let hasEmptyMessage = msgNode.items.length === 0;\n        const node = startNode(1 /* NodeTypes.Plural */, offset, loc);\n        node.cases = [];\n        node.cases.push(msgNode);\n        do {\n            const msg = parseMessage(tokenizer);\n            if (!hasEmptyMessage) {\n                hasEmptyMessage = msg.items.length === 0;\n            }\n            node.cases.push(msg);\n        } while (context.currentType !== 13 /* TokenTypes.EOF */);\n        if (hasEmptyMessage) {\n            emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);\n        }\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    function parseResource(tokenizer) {\n        const context = tokenizer.context();\n        const { offset, startLoc } = context;\n        const msgNode = parseMessage(tokenizer);\n        if (context.currentType === 13 /* TokenTypes.EOF */) {\n            return msgNode;\n        }\n        else {\n            return parsePlural(tokenizer, offset, startLoc, msgNode);\n        }\n    }\n    function parse(source) {\n        const tokenizer = createTokenizer(source, assign({}, options));\n        const context = tokenizer.context();\n        const node = startNode(0 /* NodeTypes.Resource */, context.offset, context.startLoc);\n        if (location && node.loc) {\n            node.loc.source = source;\n        }\n        node.body = parseResource(tokenizer);\n        if (options.onCacheKey) {\n            node.cacheKey = options.onCacheKey(source);\n        }\n        // assert whether achieved to EOF\n        if (context.currentType !== 13 /* TokenTypes.EOF */) {\n            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || '');\n        }\n        endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());\n        return node;\n    }\n    return { parse };\n}\nfunction getTokenCaption(token) {\n    if (token.type === 13 /* TokenTypes.EOF */) {\n        return 'EOF';\n    }\n    const name = (token.value || '').replace(/\\r?\\n/gu, '\\\\n');\n    return name.length > 10 ? name.slice(0, 9) + '…' : name;\n}\n\nfunction createTransformer(ast, options = {} // eslint-disable-line\n) {\n    const _context = {\n        ast,\n        helpers: new Set()\n    };\n    const context = () => _context;\n    const helper = (name) => {\n        _context.helpers.add(name);\n        return name;\n    };\n    return { context, helper };\n}\nfunction traverseNodes(nodes, transformer) {\n    for (let i = 0; i < nodes.length; i++) {\n        traverseNode(nodes[i], transformer);\n    }\n}\nfunction traverseNode(node, transformer) {\n    // TODO: if we need pre-hook of transform, should be implemented to here\n    switch (node.type) {\n        case 1 /* NodeTypes.Plural */:\n            traverseNodes(node.cases, transformer);\n            transformer.helper(\"plural\" /* HelperNameMap.PLURAL */);\n            break;\n        case 2 /* NodeTypes.Message */:\n            traverseNodes(node.items, transformer);\n            break;\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            traverseNode(linked.key, transformer);\n            transformer.helper(\"linked\" /* HelperNameMap.LINKED */);\n            transformer.helper(\"type\" /* HelperNameMap.TYPE */);\n            break;\n        }\n        case 5 /* NodeTypes.List */:\n            transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n            transformer.helper(\"list\" /* HelperNameMap.LIST */);\n            break;\n        case 4 /* NodeTypes.Named */:\n            transformer.helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */);\n            transformer.helper(\"named\" /* HelperNameMap.NAMED */);\n            break;\n    }\n    // TODO: if we need post-hook of transform, should be implemented to here\n}\n// transform AST\nfunction transform(ast, options = {} // eslint-disable-line\n) {\n    const transformer = createTransformer(ast);\n    transformer.helper(\"normalize\" /* HelperNameMap.NORMALIZE */);\n    // traverse\n    ast.body && traverseNode(ast.body, transformer);\n    // set meta information\n    const context = transformer.context();\n    ast.helpers = Array.from(context.helpers);\n}\n\nfunction optimize(ast) {\n    const body = ast.body;\n    if (body.type === 2 /* NodeTypes.Message */) {\n        optimizeMessageNode(body);\n    }\n    else {\n        body.cases.forEach(c => optimizeMessageNode(c));\n    }\n    return ast;\n}\nfunction optimizeMessageNode(message) {\n    if (message.items.length === 1) {\n        const item = message.items[0];\n        if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n            message.static = item.value;\n            delete item.value; // optimization for size\n        }\n    }\n    else {\n        const values = [];\n        for (let i = 0; i < message.items.length; i++) {\n            const item = message.items[i];\n            if (!(item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */)) {\n                break;\n            }\n            if (item.value == null) {\n                break;\n            }\n            values.push(item.value);\n        }\n        if (values.length === message.items.length) {\n            message.static = join(values);\n            for (let i = 0; i < message.items.length; i++) {\n                const item = message.items[i];\n                if (item.type === 3 /* NodeTypes.Text */ || item.type === 9 /* NodeTypes.Literal */) {\n                    delete item.value; // optimization for size\n                }\n            }\n        }\n    }\n}\n\nconst ERROR_DOMAIN$1 = 'minifier';\n/* eslint-disable @typescript-eslint/no-explicit-any */\nfunction minify(node) {\n    node.t = node.type;\n    switch (node.type) {\n        case 0 /* NodeTypes.Resource */: {\n            const resource = node;\n            minify(resource.body);\n            resource.b = resource.body;\n            delete resource.body;\n            break;\n        }\n        case 1 /* NodeTypes.Plural */: {\n            const plural = node;\n            const cases = plural.cases;\n            for (let i = 0; i < cases.length; i++) {\n                minify(cases[i]);\n            }\n            plural.c = cases;\n            delete plural.cases;\n            break;\n        }\n        case 2 /* NodeTypes.Message */: {\n            const message = node;\n            const items = message.items;\n            for (let i = 0; i < items.length; i++) {\n                minify(items[i]);\n            }\n            message.i = items;\n            delete message.items;\n            if (message.static) {\n                message.s = message.static;\n                delete message.static;\n            }\n            break;\n        }\n        case 3 /* NodeTypes.Text */:\n        case 9 /* NodeTypes.Literal */:\n        case 8 /* NodeTypes.LinkedModifier */:\n        case 7 /* NodeTypes.LinkedKey */: {\n            const valueNode = node;\n            if (valueNode.value) {\n                valueNode.v = valueNode.value;\n                delete valueNode.value;\n            }\n            break;\n        }\n        case 6 /* NodeTypes.Linked */: {\n            const linked = node;\n            minify(linked.key);\n            linked.k = linked.key;\n            delete linked.key;\n            if (linked.modifier) {\n                minify(linked.modifier);\n                linked.m = linked.modifier;\n                delete linked.modifier;\n            }\n            break;\n        }\n        case 5 /* NodeTypes.List */: {\n            const list = node;\n            list.i = list.index;\n            delete list.index;\n            break;\n        }\n        case 4 /* NodeTypes.Named */: {\n            const named = node;\n            named.k = named.key;\n            delete named.key;\n            break;\n        }\n        default:\n            if ((process.env.NODE_ENV !== 'production')) {\n                throw createCompileError(CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE, null, {\n                    domain: ERROR_DOMAIN$1,\n                    args: [node.type]\n                });\n            }\n    }\n    delete node.type;\n}\n/* eslint-enable @typescript-eslint/no-explicit-any */\n\n// eslint-disable-next-line @typescript-eslint/triple-slash-reference\n/// <reference types=\"source-map-js\" />\nconst ERROR_DOMAIN = 'parser';\nfunction createCodeGenerator(ast, options) {\n    const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;\n    const location = options.location !== false;\n    const _context = {\n        filename,\n        code: '',\n        column: 1,\n        line: 1,\n        offset: 0,\n        map: undefined,\n        breakLineCode,\n        needIndent: _needIndent,\n        indentLevel: 0\n    };\n    if (location && ast.loc) {\n        _context.source = ast.loc.source;\n    }\n    const context = () => _context;\n    function push(code, node) {\n        _context.code += code;\n    }\n    function _newline(n, withBreakLine = true) {\n        const _breakLineCode = withBreakLine ? breakLineCode : '';\n        push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);\n    }\n    function indent(withNewLine = true) {\n        const level = ++_context.indentLevel;\n        withNewLine && _newline(level);\n    }\n    function deindent(withNewLine = true) {\n        const level = --_context.indentLevel;\n        withNewLine && _newline(level);\n    }\n    function newline() {\n        _newline(_context.indentLevel);\n    }\n    const helper = (key) => `_${key}`;\n    const needIndent = () => _context.needIndent;\n    return {\n        context,\n        push,\n        indent,\n        deindent,\n        newline,\n        helper,\n        needIndent\n    };\n}\nfunction generateLinkedNode(generator, node) {\n    const { helper } = generator;\n    generator.push(`${helper(\"linked\" /* HelperNameMap.LINKED */)}(`);\n    generateNode(generator, node.key);\n    if (node.modifier) {\n        generator.push(`, `);\n        generateNode(generator, node.modifier);\n        generator.push(`, _type`);\n    }\n    else {\n        generator.push(`, undefined, _type`);\n    }\n    generator.push(`)`);\n}\nfunction generateMessageNode(generator, node) {\n    const { helper, needIndent } = generator;\n    generator.push(`${helper(\"normalize\" /* HelperNameMap.NORMALIZE */)}([`);\n    generator.indent(needIndent());\n    const length = node.items.length;\n    for (let i = 0; i < length; i++) {\n        generateNode(generator, node.items[i]);\n        if (i === length - 1) {\n            break;\n        }\n        generator.push(', ');\n    }\n    generator.deindent(needIndent());\n    generator.push('])');\n}\nfunction generatePluralNode(generator, node) {\n    const { helper, needIndent } = generator;\n    if (node.cases.length > 1) {\n        generator.push(`${helper(\"plural\" /* HelperNameMap.PLURAL */)}([`);\n        generator.indent(needIndent());\n        const length = node.cases.length;\n        for (let i = 0; i < length; i++) {\n            generateNode(generator, node.cases[i]);\n            if (i === length - 1) {\n                break;\n            }\n            generator.push(', ');\n        }\n        generator.deindent(needIndent());\n        generator.push(`])`);\n    }\n}\nfunction generateResource(generator, node) {\n    if (node.body) {\n        generateNode(generator, node.body);\n    }\n    else {\n        generator.push('null');\n    }\n}\nfunction generateNode(generator, node) {\n    const { helper } = generator;\n    switch (node.type) {\n        case 0 /* NodeTypes.Resource */:\n            generateResource(generator, node);\n            break;\n        case 1 /* NodeTypes.Plural */:\n            generatePluralNode(generator, node);\n            break;\n        case 2 /* NodeTypes.Message */:\n            generateMessageNode(generator, node);\n            break;\n        case 6 /* NodeTypes.Linked */:\n            generateLinkedNode(generator, node);\n            break;\n        case 8 /* NodeTypes.LinkedModifier */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 7 /* NodeTypes.LinkedKey */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 5 /* NodeTypes.List */:\n            generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"list\" /* HelperNameMap.LIST */)}(${node.index}))`, node);\n            break;\n        case 4 /* NodeTypes.Named */:\n            generator.push(`${helper(\"interpolate\" /* HelperNameMap.INTERPOLATE */)}(${helper(\"named\" /* HelperNameMap.NAMED */)}(${JSON.stringify(node.key)}))`, node);\n            break;\n        case 9 /* NodeTypes.Literal */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        case 3 /* NodeTypes.Text */:\n            generator.push(JSON.stringify(node.value), node);\n            break;\n        default:\n            if ((process.env.NODE_ENV !== 'production')) {\n                throw createCompileError(CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE, null, {\n                    domain: ERROR_DOMAIN,\n                    args: [node.type]\n                });\n            }\n    }\n}\n// generate code from AST\nconst generate = (ast, options = {}) => {\n    const mode = isString(options.mode) ? options.mode : 'normal';\n    const filename = isString(options.filename)\n        ? options.filename\n        : 'message.intl';\n    const sourceMap = !!options.sourceMap;\n    // prettier-ignore\n    const breakLineCode = options.breakLineCode != null\n        ? options.breakLineCode\n        : mode === 'arrow'\n            ? ';'\n            : '\\n';\n    const needIndent = options.needIndent ? options.needIndent : mode !== 'arrow';\n    const helpers = ast.helpers || [];\n    const generator = createCodeGenerator(ast, {\n        mode,\n        filename,\n        sourceMap,\n        breakLineCode,\n        needIndent\n    });\n    generator.push(mode === 'normal' ? `function __msg__ (ctx) {` : `(ctx) => {`);\n    generator.indent(needIndent);\n    if (helpers.length > 0) {\n        generator.push(`const { ${join(helpers.map(s => `${s}: _${s}`), ', ')} } = ctx`);\n        generator.newline();\n    }\n    generator.push(`return `);\n    generateNode(generator, ast);\n    generator.deindent(needIndent);\n    generator.push(`}`);\n    delete ast.helpers;\n    const { code, map } = generator.context();\n    return {\n        ast,\n        code,\n        map: map ? map.toJSON() : undefined // eslint-disable-line @typescript-eslint/no-explicit-any\n    };\n};\n\nfunction baseCompile(source, options = {}) {\n    const assignedOptions = assign({}, options);\n    const jit = !!assignedOptions.jit;\n    const enalbeMinify = !!assignedOptions.minify;\n    const enambeOptimize = assignedOptions.optimize == null ? true : assignedOptions.optimize;\n    // parse source codes\n    const parser = createParser(assignedOptions);\n    const ast = parser.parse(source);\n    if (!jit) {\n        // transform ASTs\n        transform(ast, assignedOptions);\n        // generate javascript codes\n        return generate(ast, assignedOptions);\n    }\n    else {\n        // optimize ASTs\n        enambeOptimize && optimize(ast);\n        // minimize ASTs\n        enalbeMinify && minify(ast);\n        // In JIT mode, no ast transform, no code generation.\n        return { ast, code: '' };\n    }\n}\n\nexport { COMPILE_ERROR_CODES_EXTEND_POINT, CompileErrorCodes, ERROR_DOMAIN$2 as ERROR_DOMAIN, LOCATION_STUB, baseCompile, createCompileError, createLocation, createParser, createPosition, defaultOnError, detectHtmlTag, errorMessages };\n"], "mappings": ";;;;;;;;AAOA,IAAM,gBAAgB;AAAA,EAClB,OAAO,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AAAA,EACvC,KAAK,EAAE,MAAM,GAAG,QAAQ,GAAG,QAAQ,EAAE;AACzC;AACA,SAAS,eAAe,MAAM,QAAQ,QAAQ;AAC1C,SAAO,EAAE,MAAM,QAAQ,OAAO;AAClC;AACA,SAAS,eAAe,OAAO,KAAK,QAAQ;AACxC,QAAM,MAAM,EAAE,OAAO,IAAI;AACzB,MAAI,UAAU,MAAM;AAChB,QAAI,SAAS;AAAA,EACjB;AACA,SAAO;AACX;AAEA,IAAM,oBAAoB;AAAA;AAAA,EAEtB,gBAAgB;AAAA,EAChB,8BAA8B;AAAA,EAC9B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,iCAAiC;AAAA,EACjC,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,mBAAmB;AAAA,EACnB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA;AAAA,EAEvB,8BAA8B;AAAA,EAC9B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA;AAAA,EAE7B,6BAA6B;AAAA;AAAA,EAE7B,8BAA8B;AAClC;AAIA,IAAM,mCAAmC;AAEzC,IAAM,gBAAgB;AAAA;AAAA,EAElB,CAAC,kBAAkB,cAAc,GAAG;AAAA,EACpC,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,EAClD,CAAC,kBAAkB,wCAAwC,GAAG;AAAA,EAC9D,CAAC,kBAAkB,uBAAuB,GAAG;AAAA,EAC7C,CAAC,kBAAkB,+BAA+B,GAAG;AAAA,EACrD,CAAC,kBAAkB,wBAAwB,GAAG;AAAA,EAC9C,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,EAChD,CAAC,kBAAkB,iBAAiB,GAAG;AAAA,EACvC,CAAC,kBAAkB,0BAA0B,GAAG;AAAA,EAChD,CAAC,kBAAkB,qBAAqB,GAAG;AAAA;AAAA,EAE3C,CAAC,kBAAkB,4BAA4B,GAAG;AAAA,EAClD,CAAC,kBAAkB,gCAAgC,GAAG;AAAA,EACtD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA,EACjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA;AAAA,EAEjD,CAAC,kBAAkB,2BAA2B,GAAG;AAAA;AAAA,EAEjD,CAAC,kBAAkB,4BAA4B,GAAG;AACtD;AACA,SAAS,mBAAmB,MAAM,KAAK,UAAU,CAAC,GAAG;AACjD,QAAM,EAAE,QAAQ,UAAU,KAAK,IAAI;AACnC,QAAM,MAAO,OACP,QAAQ,YAAY,eAAe,IAAI,KAAK,IAAI,GAAI,QAAQ,CAAC,CAAE,IAC/D;AACN,QAAM,QAAQ,IAAI,YAAY,OAAO,GAAG,CAAC;AACzC,QAAM,OAAO;AACb,MAAI,KAAK;AACL,UAAM,WAAW;AAAA,EACrB;AACA,QAAM,SAAS;AACf,SAAO;AACX;AAEA,SAAS,eAAe,OAAO;AAC3B,QAAM;AACV;AAGA,IAAM,cAAc;AACpB,IAAM,gBAAgB,CAAC,WAAW,YAAY,KAAK,MAAM;AAEzD,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,IAAM,UAAU,OAAO,aAAa,IAAM;AAC1C,SAAS,cAAc,KAAK;AACxB,QAAM,OAAO;AACb,MAAI,SAAS;AACb,MAAI,QAAQ;AACZ,MAAI,UAAU;AACd,MAAI,cAAc;AAClB,QAAM,SAAS,CAACA,WAAU,KAAKA,MAAK,MAAM,WAAW,KAAKA,SAAQ,CAAC,MAAM;AACzE,QAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,QAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,QAAM,OAAO,CAACA,WAAU,KAAKA,MAAK,MAAM;AACxC,QAAM,YAAY,CAACA,WAAU,OAAOA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK,KAAK,KAAKA,MAAK;AACtF,QAAM,QAAQ,MAAM;AACpB,QAAM,OAAO,MAAM;AACnB,QAAM,SAAS,MAAM;AACrB,QAAM,aAAa,MAAM;AACzB,QAAM,SAAS,CAAC,WAAW,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM;AACjG,QAAM,cAAc,MAAM,OAAO,MAAM;AACvC,QAAM,cAAc,MAAM,OAAO,SAAS,WAAW;AACrD,WAAS,OAAO;AACZ,kBAAc;AACd,QAAI,UAAU,MAAM,GAAG;AACnB;AACA,gBAAU;AAAA,IACd;AACA,QAAI,OAAO,MAAM,GAAG;AAChB;AAAA,IACJ;AACA;AACA;AACA,WAAO,KAAK,MAAM;AAAA,EACtB;AACA,WAAS,OAAO;AACZ,QAAI,OAAO,SAAS,WAAW,GAAG;AAC9B;AAAA,IACJ;AACA;AACA,WAAO,KAAK,SAAS,WAAW;AAAA,EACpC;AACA,WAAS,QAAQ;AACb,aAAS;AACT,YAAQ;AACR,cAAU;AACV,kBAAc;AAAA,EAClB;AACA,WAAS,UAAU,SAAS,GAAG;AAC3B,kBAAc;AAAA,EAClB;AACA,WAAS,aAAa;AAClB,UAAM,SAAS,SAAS;AACxB,WAAO,WAAW,QAAQ;AACtB,WAAK;AAAA,IACT;AACA,kBAAc;AAAA,EAClB;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,oBAAoB;AAC1B,IAAM,iBAAiB;AACvB,SAAS,gBAAgB,QAAQ,UAAU,CAAC,GAAG;AAC3C,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,QAAQ,cAAc,MAAM;AAClC,QAAM,gBAAgB,MAAM,MAAM,MAAM;AACxC,QAAM,kBAAkB,MAAM,eAAe,MAAM,KAAK,GAAG,MAAM,OAAO,GAAG,MAAM,MAAM,CAAC;AACxF,QAAM,WAAW,gBAAgB;AACjC,QAAM,cAAc,cAAc;AAClC,QAAM,WAAW;AAAA,IACb,aAAa;AAAA,IACb,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,UAAU;AAAA,IACV,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,UAAU;AAAA,IACV,MAAM;AAAA,EACV;AACA,QAAM,UAAU,MAAM;AACtB,QAAM,EAAE,QAAQ,IAAI;AACpB,WAAS,UAAU,MAAM,KAAK,WAAW,MAAM;AAC3C,UAAM,MAAM,QAAQ;AACpB,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,WAAW,eAAe,IAAI,UAAU,GAAG,IAAI;AAC3D,YAAM,MAAM,mBAAmB,MAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,cAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACA,WAAS,SAASC,UAAS,MAAM,OAAO;AACpC,IAAAA,SAAQ,SAAS,gBAAgB;AACjC,IAAAA,SAAQ,cAAc;AACtB,UAAM,QAAQ,EAAE,KAAK;AACrB,QAAI,UAAU;AACV,YAAM,MAAM,eAAeA,SAAQ,UAAUA,SAAQ,MAAM;AAAA,IAC/D;AACA,QAAI,SAAS,MAAM;AACf,YAAM,QAAQ;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,QAAM,cAAc,CAACA,aAAY;AAAA,IAASA;AAAA,IAAS;AAAA;AAAA,EAAuB;AAC1E,WAAS,IAAI,MAAM,IAAI;AACnB,QAAI,KAAK,YAAY,MAAM,IAAI;AAC3B,WAAK,KAAK;AACV,aAAO;AAAA,IACX,OACK;AACD,gBAAU,kBAAkB,gBAAgB,gBAAgB,GAAG,GAAG,EAAE;AACpE,aAAO;AAAA,IACX;AAAA,EACJ;AACA,WAAS,WAAW,MAAM;AACtB,QAAI,MAAM;AACV,WAAO,KAAK,YAAY,MAAM,WAAW,KAAK,YAAY,MAAM,SAAS;AACrE,aAAO,KAAK,YAAY;AACxB,WAAK,KAAK;AAAA,IACd;AACA,WAAO;AAAA,EACX;AACA,WAAS,WAAW,MAAM;AACtB,UAAM,MAAM,WAAW,IAAI;AAC3B,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AACA,WAAS,kBAAkB,IAAI;AAC3B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,EAEf;AACA,WAAS,cAAc,IAAI;AACvB,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B;AACA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,KAAK,KAAK,YAAY,MAAM,MAAM,KAAK,KAAK,IAAI,KAAK,YAAY;AACvE,UAAM,MAAM,cAAc,EAAE;AAC5B,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,eAAe,MAAMA,UAAS;AACnC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,iBAAiB,MAAMA,UAAS;AACrC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAgC;AAChD,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,sBAAsB,MAAMA,UAAS;AAC1C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAA8B;AAC9C,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,kBAAkB,KAAK,YAAY,CAAC;AAChD,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,EAAE,gBAAgB,KAClB,gBAAgB,KAAqC;AACrD,aAAO;AAAA,IACX;AACA,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAMA,UAAS;AACvC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,gBAAgB,GAAoC;AACpD,aAAO;AAAA,IACX;AACA,UAAM,KAAK,MAAM;AACb,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,KAAgC;AACvC,eAAO,kBAAkB,KAAK,KAAK,CAAC;AAAA,MACxC,WACS,OAAO,OACZ,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,WACP,CAAC,IAAI;AACL,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG;AAAA,MACd,OACK;AAED,eAAO,YAAY,MAAM,KAAK;AAAA,MAClC;AAAA,IACJ;AACA,UAAM,MAAM,GAAG;AACf,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,cAAc,MAAM;AACzB,eAAW,IAAI;AACf,UAAM,MAAM,KAAK,YAAY,MAAM;AACnC,SAAK,UAAU;AACf,WAAO;AAAA,EACX;AACA,WAAS,YAAY,MAAM,QAAQ,MAAM;AACrC,UAAM,KAAK,CAAC,WAAW,OAAO,OAAO,OAAO;AACxC,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,KAAgC;AACvC,eAAO;AAAA,MACX,WACS,OAAO,OAAoC,CAAC,IAAI;AACrD,eAAO;AAAA,MACX,WACS,OAAO,KAA2B;AACvC,eAAO,EAAE,SAAS,WAAW,SAAS;AAAA,MAC1C,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,OAAO;AAAA,MAC3B,WACS,OAAO,SAAS;AACrB,aAAK,KAAK;AACV,eAAO,GAAG,MAAM,OAAO;AAAA,MAC3B,OACK;AACD,eAAO;AAAA,MACX;AAAA,IACJ;AACA,UAAM,MAAM,GAAG;AACf,aAAS,KAAK,UAAU;AACxB,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM,IAAI;AACxB,UAAM,KAAK,KAAK,YAAY;AAC5B,QAAI,OAAO,KAAK;AACZ,aAAO;AAAA,IACX;AACA,QAAI,GAAG,EAAE,GAAG;AACR,WAAK,KAAK;AACV,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,WAAS,aAAa,IAAI;AACtB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,EAEf;AACA,WAAS,mBAAmB,MAAM;AAC9B,WAAO,SAAS,MAAM,YAAY;AAAA,EACtC;AACA,WAAS,kBAAkB,IAAI;AAC3B,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,IACnB,OAAO;AAAA,IACP,OAAO;AAAA,IACP,OAAO;AAAA,EAEf;AACA,WAAS,wBAAwB,MAAM;AACnC,WAAO,SAAS,MAAM,iBAAiB;AAAA,EAC3C;AACA,WAAS,QAAQ,IAAI;AACjB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAO,MAAM,MAAM,MAAM;AAAA,EAC7B;AACA,WAAS,UAAU,MAAM;AACrB,WAAO,SAAS,MAAM,OAAO;AAAA,EACjC;AACA,WAAS,WAAW,IAAI;AACpB,UAAM,KAAK,GAAG,WAAW,CAAC;AAC1B,WAAS,MAAM,MAAM,MAAM;AAAA,IACtB,MAAM,MAAM,MAAM;AAAA,IAClB,MAAM,MAAM,MAAM;AAAA,EAC3B;AACA,WAAS,aAAa,MAAM;AACxB,WAAO,SAAS,MAAM,UAAU;AAAA,EACpC;AACA,WAAS,UAAU,MAAM;AACrB,QAAI,KAAK;AACT,QAAI,MAAM;AACV,WAAQ,KAAK,UAAU,IAAI,GAAI;AAC3B,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AACA,WAAS,SAAS,MAAM;AACpB,QAAI,MAAM;AACV,WAAO,MAAM;AACT,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL;AAAA,MACJ,WACS,OAAO,WAAW,OAAO,SAAS;AACvC,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO;AACP,eAAK,KAAK;AAAA,QACd,WACS,cAAc,IAAI,GAAG;AAC1B;AAAA,QACJ,OACK;AACD,iBAAO;AACP,eAAK,KAAK;AAAA,QACd;AAAA,MACJ,OACK;AACD,eAAO;AACP,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB,MAAM;AAC/B,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,wBAAwB,IAAI,GAAI;AACzC,cAAQ;AAAA,IACZ;AACA,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,gBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,IAChF;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,eAAW,IAAI;AACf,QAAI,QAAQ;AACZ,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,WAAK,KAAK;AACV,eAAS,IAAI,UAAU,IAAI,CAAC;AAAA,IAChC,OACK;AACD,eAAS,UAAU,IAAI;AAAA,IAC3B;AACA,QAAI,KAAK,YAAY,MAAM,KAAK;AAC5B,gBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,IAChF;AACA,WAAO;AAAA,EACX;AACA,WAAS,UAAU,IAAI;AACnB,WAAO,OAAO,qBAAqB,OAAO;AAAA,EAC9C;AACA,WAAS,YAAY,MAAM;AACvB,eAAW,IAAI;AAEf,QAAI,MAAM,GAAI;AACd,QAAI,KAAK;AACT,QAAI,UAAU;AACd,WAAQ,KAAK,SAAS,MAAM,SAAS,GAAI;AACrC,UAAI,OAAO,MAAM;AACb,mBAAW,mBAAmB,IAAI;AAAA,MACtC,OACK;AACD,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,UAAM,UAAU,KAAK,YAAY;AACjC,QAAI,YAAY,WAAW,YAAY,KAAK;AACxC,gBAAU,kBAAkB,0CAA0C,gBAAgB,GAAG,CAAC;AAE1F,UAAI,YAAY,SAAS;AACrB,aAAK,KAAK;AAEV,YAAI,MAAM,GAAI;AAAA,MAClB;AACA,aAAO;AAAA,IACX;AAEA,QAAI,MAAM,GAAI;AACd,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ,IAAI;AAAA,MACR,KAAK;AAAA,MACL,KAAK;AACD,aAAK,KAAK;AACV,eAAO,KAAK,EAAE;AAAA,MAClB,KAAK;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,MAChD,KAAK;AACD,eAAO,0BAA0B,MAAM,IAAI,CAAC;AAAA,MAChD;AACI,kBAAU,kBAAkB,yBAAyB,gBAAgB,GAAG,GAAG,EAAE;AAC7E,eAAO;AAAA,IACf;AAAA,EACJ;AACA,WAAS,0BAA0B,MAAM,SAAS,QAAQ;AACtD,QAAI,MAAM,OAAO;AACjB,QAAI,WAAW;AACf,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,YAAM,KAAK,aAAa,IAAI;AAC5B,UAAI,CAAC,IAAI;AACL,kBAAU,kBAAkB,iCAAiC,gBAAgB,GAAG,GAAG,KAAK,OAAO,GAAG,QAAQ,GAAG,KAAK,YAAY,CAAC,EAAE;AACjI;AAAA,MACJ;AACA,kBAAY;AAAA,IAChB;AACA,WAAO,KAAK,OAAO,GAAG,QAAQ;AAAA,EAClC;AACA,WAAS,oBAAoB,IAAI;AAC7B,WAAQ,OAAO,OACX,OAAO,OACP,OAAO,WACP,OAAO;AAAA,EACf;AACA,WAAS,sBAAsB,MAAM;AACjC,eAAW,IAAI;AACf,QAAI,KAAK;AACT,QAAI,cAAc;AAClB,WAAQ,KAAK,SAAS,MAAM,mBAAmB,GAAI;AAC/C,qBAAe;AAAA,IACnB;AACA,WAAO;AAAA,EACX;AACA,WAAS,mBAAmB,MAAM;AAC9B,QAAI,KAAK;AACT,QAAI,OAAO;AACX,WAAQ,KAAK,mBAAmB,IAAI,GAAI;AACpC,cAAQ;AAAA,IACZ;AACA,WAAO;AAAA,EACX;AACA,WAAS,gBAAgB,MAAM;AAC3B,UAAM,KAAK,CAAC,QAAQ;AAChB,YAAM,KAAK,KAAK,YAAY;AAC5B,UAAI,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,OAAO,OACP,CAAC,IAAI;AACL,eAAO;AAAA,MACX,WACS,OAAO,SAAS;AACrB,eAAO;AAAA,MACX,WACS,OAAO,WAAW,OAAO,KAAK;AACnC,eAAO;AACP,aAAK,KAAK;AACV,eAAO,GAAG,GAAG;AAAA,MACjB,OACK;AACD,eAAO;AACP,aAAK,KAAK;AACV,eAAO,GAAG,GAAG;AAAA,MACjB;AAAA,IACJ;AACA,WAAO,GAAG,EAAE;AAAA,EAChB;AACA,WAAS,WAAW,MAAM;AACtB,eAAW,IAAI;AACf,UAAM,SAAS;AAAA,MAAI;AAAA,MAAM;AAAA;AAAA,IAAyB;AAClD,eAAW,IAAI;AACf,WAAO;AAAA,EACX;AAEA,WAAS,uBAAuB,MAAMA,UAAS;AAC3C,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ,IAAI;AAAA,MACR,KAAK;AACD,YAAIA,SAAQ,aAAa,GAAG;AACxB,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,QAChF;AACA,aAAK,KAAK;AACV,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAA8B;AAAA;AAAA,QAA8B;AACtF,mBAAW,IAAI;AACf,QAAAA,SAAQ;AACR,eAAO;AAAA,MACX,KAAK;AACD,YAAIA,SAAQ,YAAY,KACpBA,SAAQ,gBAAgB,GAA8B;AACtD,oBAAU,kBAAkB,mBAAmB,gBAAgB,GAAG,CAAC;AAAA,QACvE;AACA,aAAK,KAAK;AACV,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAA+B;AAAA;AAAA,QAA+B;AACxF,QAAAA,SAAQ;AACR,QAAAA,SAAQ,YAAY,KAAK,WAAW,IAAI;AACxC,YAAIA,SAAQ,YAAYA,SAAQ,cAAc,GAAG;AAC7C,UAAAA,SAAQ,WAAW;AAAA,QACvB;AACA,eAAO;AAAA,MACX,KAAK;AACD,YAAIA,SAAQ,YAAY,GAAG;AACvB,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,QAChF;AACA,gBAAQ,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAC/D,QAAAA,SAAQ,YAAY;AACpB,eAAO;AAAA,MACX,SAAS;AACL,YAAI,uBAAuB;AAC3B,YAAI,sBAAsB;AAC1B,YAAI,eAAe;AACnB,YAAI,cAAc,IAAI,GAAG;AACrB,cAAIA,SAAQ,YAAY,GAAG;AACvB,sBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAAA,UAChF;AACA,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAIA,SAAQ,YAAY,MACnBA,SAAQ,gBAAgB,KACrBA,SAAQ,gBAAgB,KACxBA,SAAQ,gBAAgB,IAA6B;AACzD,oBAAU,kBAAkB,4BAA4B,gBAAgB,GAAG,CAAC;AAC5E,UAAAA,SAAQ,YAAY;AACpB,iBAAO,UAAU,MAAMA,QAAO;AAAA,QAClC;AACA,YAAK,uBAAuB,uBAAuB,MAAMA,QAAO,GAAI;AAChE,kBAAQ,SAASA,UAAS,GAA0B,oBAAoB,IAAI,CAAC;AAC7E,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAK,sBAAsB,sBAAsB,MAAMA,QAAO,GAAI;AAC9D,kBAAQ,SAASA,UAAS,GAAyB,mBAAmB,IAAI,CAAC;AAC3E,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAK,eAAe,eAAe,MAAMA,QAAO,GAAI;AAChD,kBAAQ,SAASA,UAAS,GAA4B,YAAY,IAAI,CAAC;AACvE,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,wBAAwB,CAAC,uBAAuB,CAAC,cAAc;AAEhE,kBAAQ,SAASA,UAAS,IAAkC,sBAAsB,IAAI,CAAC;AACvF,oBAAU,kBAAkB,8BAA8B,gBAAgB,GAAG,GAAG,MAAM,KAAK;AAC3F,qBAAW,IAAI;AACf,iBAAO;AAAA,QACX;AACA;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAEA,WAAS,kBAAkB,MAAMA,UAAS;AACtC,UAAM,EAAE,YAAY,IAAIA;AACxB,QAAI,QAAQ;AACZ,UAAM,KAAK,KAAK,YAAY;AAC5B,SAAK,gBAAgB,KACjB,gBAAgB,KAChB,gBAAgB,MAChB,gBAAgB,OACf,OAAO,WAAW,OAAO,UAAU;AACpC,gBAAU,kBAAkB,uBAAuB,gBAAgB,GAAG,CAAC;AAAA,IAC3E;AACA,YAAQ,IAAI;AAAA,MACR,KAAK;AACD,aAAK,KAAK;AACV,gBAAQ;AAAA,UAASA;AAAA,UAAS;AAAA,UAAgC;AAAA;AAAA,QAAgC;AAC1F,QAAAA,SAAQ,WAAW;AACnB,eAAO;AAAA,MACX,KAAK;AACD,mBAAW,IAAI;AACf,aAAK,KAAK;AACV,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAA8B;AAAA;AAAA,QAA8B;AAAA,MACzF,KAAK;AACD,mBAAW,IAAI;AACf,aAAK,KAAK;AACV,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAAoC;AAAA;AAAA,QAAoC;AAAA,MACrG;AACI,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,MAAMA,QAAO,KAC9B,uBAAuB,MAAMA,QAAO,GAAG;AACvC,qBAAW,IAAI;AACf,iBAAO,kBAAkB,MAAMA,QAAO;AAAA,QAC1C;AACA,YAAI,sBAAsB,MAAMA,QAAO,GAAG;AACtC,qBAAW,IAAI;AACf,iBAAO,SAASA,UAAS,IAAoC,mBAAmB,IAAI,CAAC;AAAA,QACzF;AACA,YAAI,mBAAmB,MAAMA,QAAO,GAAG;AACnC,qBAAW,IAAI;AACf,cAAI,OAAO,KAAgC;AAEvC,mBAAO,uBAAuB,MAAMA,QAAO,KAAK;AAAA,UACpD,OACK;AACD,mBAAO,SAASA,UAAS,IAA+B,gBAAgB,IAAI,CAAC;AAAA,UACjF;AAAA,QACJ;AACA,YAAI,gBAAgB,GAAgC;AAChD,oBAAU,kBAAkB,uBAAuB,gBAAgB,GAAG,CAAC;AAAA,QAC3E;AACA,QAAAA,SAAQ,YAAY;AACpB,QAAAA,SAAQ,WAAW;AACnB,eAAO,UAAU,MAAMA,QAAO;AAAA,IACtC;AAAA,EACJ;AAEA,WAAS,UAAU,MAAMA,UAAS;AAC9B,QAAI,QAAQ;AAAA,MAAE,MAAM;AAAA;AAAA,IAAwB;AAC5C,QAAIA,SAAQ,YAAY,GAAG;AACvB,aAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IACvE;AACA,QAAIA,SAAQ,UAAU;AAClB,aAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,IAClE;AACA,UAAM,KAAK,KAAK,YAAY;AAC5B,YAAQ,IAAI;AAAA,MACR,KAAK;AACD,eAAO,uBAAuB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,MACvE,KAAK;AACD,kBAAU,kBAAkB,0BAA0B,gBAAgB,GAAG,CAAC;AAC1E,aAAK,KAAK;AACV,eAAO;AAAA,UAASA;AAAA,UAAS;AAAA,UAA+B;AAAA;AAAA,QAA+B;AAAA,MAC3F,KAAK;AACD,eAAO,kBAAkB,MAAMA,QAAO,KAAK,YAAYA,QAAO;AAAA,MAClE,SAAS;AACL,YAAI,cAAc,IAAI,GAAG;AACrB,kBAAQ,SAASA,UAAS,GAAyB,WAAW,IAAI,CAAC;AAEnE,UAAAA,SAAQ,YAAY;AACpB,UAAAA,SAAQ,WAAW;AACnB,iBAAO;AAAA,QACX;AACA,YAAI,YAAY,IAAI,GAAG;AACnB,iBAAO,SAASA,UAAS,GAAyB,SAAS,IAAI,CAAC;AAAA,QACpE;AACA;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACA,WAAS,YAAY;AACjB,UAAM,EAAE,aAAa,QAAQ,UAAU,OAAO,IAAI;AAClD,aAAS,WAAW;AACpB,aAAS,aAAa;AACtB,aAAS,eAAe;AACxB,aAAS,aAAa;AACtB,aAAS,SAAS,cAAc;AAChC,aAAS,WAAW,gBAAgB;AACpC,QAAI,MAAM,YAAY,MAAM,KAAK;AAC7B,aAAO;AAAA,QAAS;AAAA,QAAU;AAAA;AAAA,MAAuB;AAAA,IACrD;AACA,WAAO,UAAU,OAAO,QAAQ;AAAA,EACpC;AACA,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB;AAEvB,IAAM,gBAAgB;AACtB,SAAS,mBAAmB,OAAO,YAAY,YAAY;AACvD,UAAQ,OAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA;AAAA,IAEX,KAAK;AAED,aAAO;AAAA,IACX,SAAS;AACL,YAAM,YAAY,SAAS,cAAc,YAAY,EAAE;AACvD,UAAI,aAAa,SAAU,aAAa,OAAQ;AAC5C,eAAO,OAAO,cAAc,SAAS;AAAA,MACzC;AAGA,aAAO;AAAA,IACX;AAAA,EACJ;AACJ;AACA,SAAS,aAAa,UAAU,CAAC,GAAG;AAChC,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,EAAE,QAAQ,IAAI;AACpB,WAAS,UAAU,UAAU,MAAM,OAAO,WAAW,MAAM;AACvD,UAAM,MAAM,SAAS,gBAAgB;AACrC,QAAI,UAAU;AACd,QAAI,UAAU;AACd,QAAI,SAAS;AACT,YAAM,MAAM,WAAW,eAAe,OAAO,GAAG,IAAI;AACpD,YAAM,MAAM,mBAAmB,MAAM,KAAK;AAAA,QACtC,QAAQ;AAAA,QACR;AAAA,MACJ,CAAC;AACD,cAAQ,GAAG;AAAA,IACf;AAAA,EACJ;AACA,WAAS,UAAU,MAAM,QAAQ,KAAK;AAClC,UAAM,OAAO,EAAE,KAAK;AACpB,QAAI,UAAU;AACV,WAAK,QAAQ;AACb,WAAK,MAAM;AACX,WAAK,MAAM,EAAE,OAAO,KAAK,KAAK,IAAI;AAAA,IACtC;AACA,WAAO;AAAA,EACX;AACA,WAAS,QAAQ,MAAM,QAAQ,KAAK,MAAM;AACtC,QAAI,UAAU;AACV,WAAK,MAAM;AACX,UAAI,KAAK,KAAK;AACV,aAAK,IAAI,MAAM;AAAA,MACnB;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAAwB,QAAQ,QAAQ,QAAQ,QAAQ;AAC/E,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,UAAU,WAAW,OAAO;AACjC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAwB,QAAQ,GAAG;AAC1D,SAAK,QAAQ,SAAS,OAAO,EAAE;AAC/B,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,WAAW,WAAW,KAAK;AAChC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAyB,QAAQ,GAAG;AAC3D,SAAK,MAAM;AACX,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,aAAa,WAAW,OAAO;AACpC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAA2B,QAAQ,GAAG;AAC7D,SAAK,QAAQ,MAAM,QAAQ,eAAe,kBAAkB;AAC5D,cAAU,UAAU;AACpB,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,oBAAoB,WAAW;AACpC,UAAM,QAAQ,UAAU,UAAU;AAClC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,YAAY,QAAQ,cAAc,IAAI,IAAI;AAClD,UAAM,OAAO,UAAU,GAAkC,QAAQ,GAAG;AACpE,QAAI,MAAM,SAAS,IAAoC;AAEnD,gBAAU,WAAW,kBAAkB,kCAAkC,QAAQ,cAAc,CAAC;AAChG,WAAK,QAAQ;AACb,cAAQ,MAAM,QAAQ,GAAG;AACzB,aAAO;AAAA,QACH,kBAAkB;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AAEA,QAAI,MAAM,SAAS,MAAM;AACrB,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IACvH;AACA,SAAK,QAAQ,MAAM,SAAS;AAC5B,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,MACH;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,eAAe,WAAW,OAAO;AACtC,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAA6B,QAAQ,QAAQ,QAAQ,QAAQ;AACpF,SAAK,QAAQ;AACb,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,YAAY,WAAW;AAC5B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,aAAa,UAAU,GAA0B,QAAQ,QAAQ,QAAQ,QAAQ;AACvF,QAAI,QAAQ,UAAU,UAAU;AAChC,QAAI,MAAM,SAAS,GAA8B;AAC7C,YAAM,SAAS,oBAAoB,SAAS;AAC5C,iBAAW,WAAW,OAAO;AAC7B,cAAQ,OAAO,oBAAoB,UAAU,UAAU;AAAA,IAC3D;AAEA,QAAI,MAAM,SAAS,GAAoC;AACnD,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,IACvH;AACA,YAAQ,UAAU,UAAU;AAE5B,QAAI,MAAM,SAAS,GAA8B;AAC7C,cAAQ,UAAU,UAAU;AAAA,IAChC;AACA,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,eAAe,WAAW,MAAM,SAAS,EAAE;AAC5D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,WAAW,WAAW,MAAM,SAAS,EAAE;AACxD;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,UAAU,WAAW,MAAM,SAAS,EAAE;AACvD;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,SAAS,MAAM;AACrB,oBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,QACvH;AACA,mBAAW,MAAM,aAAa,WAAW,MAAM,SAAS,EAAE;AAC1D;AAAA,MACJ,SAAS;AAEL,kBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,CAAC;AAC3F,cAAM,cAAc,UAAU,QAAQ;AACtC,cAAM,qBAAqB,UAAU,GAA6B,YAAY,QAAQ,YAAY,QAAQ;AAC1G,2BAAmB,QAAQ;AAC3B,gBAAQ,oBAAoB,YAAY,QAAQ,YAAY,QAAQ;AACpE,mBAAW,MAAM;AACjB,gBAAQ,YAAY,YAAY,QAAQ,YAAY,QAAQ;AAC5D,eAAO;AAAA,UACH,kBAAkB;AAAA,UAClB,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,IACJ;AACA,YAAQ,YAAY,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AAC1E,WAAO;AAAA,MACH,MAAM;AAAA,IACV;AAAA,EACJ;AACA,WAAS,aAAa,WAAW;AAC7B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,cAAc,QAAQ,gBAAgB,IACtC,UAAU,cAAc,IACxB,QAAQ;AACd,UAAM,WAAW,QAAQ,gBAAgB,IACnC,QAAQ,SACR,QAAQ;AACd,UAAM,OAAO,UAAU,GAA2B,aAAa,QAAQ;AACvE,SAAK,QAAQ,CAAC;AACd,QAAI,YAAY;AAChB,OAAG;AACC,YAAM,QAAQ,aAAa,UAAU,UAAU;AAC/C,kBAAY;AACZ,cAAQ,MAAM,MAAM;AAAA,QAChB,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,UAAU,WAAW,MAAM,SAAS,EAAE,CAAC;AACvD;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,WAAW,WAAW,MAAM,SAAS,EAAE,CAAC;AACxD;AAAA,QACJ,KAAK;AACD,cAAI,MAAM,SAAS,MAAM;AACrB,sBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,gBAAgB,KAAK,CAAC;AAAA,UACvH;AACA,eAAK,MAAM,KAAK,aAAa,WAAW,MAAM,SAAS,EAAE,CAAC;AAC1D;AAAA,QACJ,KAAK,GAAgC;AACjC,gBAAM,SAAS,YAAY,SAAS;AACpC,eAAK,MAAM,KAAK,OAAO,IAAI;AAC3B,sBAAY,OAAO,oBAAoB;AACvC;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ,SAAS,QAAQ,gBAAgB,MAC7B,QAAQ,gBAAgB;AAE5B,UAAM,YAAY,QAAQ,gBAAgB,IACpC,QAAQ,aACR,UAAU,cAAc;AAC9B,UAAM,SAAS,QAAQ,gBAAgB,IACjC,QAAQ,aACR,UAAU,gBAAgB;AAChC,YAAQ,MAAM,WAAW,MAAM;AAC/B,WAAO;AAAA,EACX;AACA,WAAS,YAAY,WAAW,QAAQ,KAAK,SAAS;AAClD,UAAM,UAAU,UAAU,QAAQ;AAClC,QAAI,kBAAkB,QAAQ,MAAM,WAAW;AAC/C,UAAM,OAAO,UAAU,GAA0B,QAAQ,GAAG;AAC5D,SAAK,QAAQ,CAAC;AACd,SAAK,MAAM,KAAK,OAAO;AACvB,OAAG;AACC,YAAM,MAAM,aAAa,SAAS;AAClC,UAAI,CAAC,iBAAiB;AAClB,0BAAkB,IAAI,MAAM,WAAW;AAAA,MAC3C;AACA,WAAK,MAAM,KAAK,GAAG;AAAA,IACvB,SAAS,QAAQ,gBAAgB;AACjC,QAAI,iBAAiB;AACjB,gBAAU,WAAW,kBAAkB,8BAA8B,KAAK,CAAC;AAAA,IAC/E;AACA,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,WAAS,cAAc,WAAW;AAC9B,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,EAAE,QAAQ,SAAS,IAAI;AAC7B,UAAM,UAAU,aAAa,SAAS;AACtC,QAAI,QAAQ,gBAAgB,IAAyB;AACjD,aAAO;AAAA,IACX,OACK;AACD,aAAO,YAAY,WAAW,QAAQ,UAAU,OAAO;AAAA,IAC3D;AAAA,EACJ;AACA,WAAS,MAAM,QAAQ;AACnB,UAAM,YAAY,gBAAgB,QAAQ,OAAO,CAAC,GAAG,OAAO,CAAC;AAC7D,UAAM,UAAU,UAAU,QAAQ;AAClC,UAAM,OAAO,UAAU,GAA4B,QAAQ,QAAQ,QAAQ,QAAQ;AACnF,QAAI,YAAY,KAAK,KAAK;AACtB,WAAK,IAAI,SAAS;AAAA,IACtB;AACA,SAAK,OAAO,cAAc,SAAS;AACnC,QAAI,QAAQ,YAAY;AACpB,WAAK,WAAW,QAAQ,WAAW,MAAM;AAAA,IAC7C;AAEA,QAAI,QAAQ,gBAAgB,IAAyB;AACjD,gBAAU,WAAW,kBAAkB,6BAA6B,QAAQ,cAAc,GAAG,OAAO,QAAQ,MAAM,KAAK,EAAE;AAAA,IAC7H;AACA,YAAQ,MAAM,UAAU,cAAc,GAAG,UAAU,gBAAgB,CAAC;AACpE,WAAO;AAAA,EACX;AACA,SAAO,EAAE,MAAM;AACnB;AACA,SAAS,gBAAgB,OAAO;AAC5B,MAAI,MAAM,SAAS,IAAyB;AACxC,WAAO;AAAA,EACX;AACA,QAAM,QAAQ,MAAM,SAAS,IAAI,QAAQ,WAAW,KAAK;AACzD,SAAO,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,CAAC,IAAI,MAAM;AACvD;AAEA,SAAS,kBAAkB,KAAK,UAAU,CAAC,GACzC;AACE,QAAM,WAAW;AAAA,IACb;AAAA,IACA,SAAS,oBAAI,IAAI;AAAA,EACrB;AACA,QAAM,UAAU,MAAM;AACtB,QAAM,SAAS,CAAC,SAAS;AACrB,aAAS,QAAQ,IAAI,IAAI;AACzB,WAAO;AAAA,EACX;AACA,SAAO,EAAE,SAAS,OAAO;AAC7B;AACA,SAAS,cAAc,OAAO,aAAa;AACvC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,iBAAa,MAAM,CAAC,GAAG,WAAW;AAAA,EACtC;AACJ;AACA,SAAS,aAAa,MAAM,aAAa;AAErC,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC,kBAAY;AAAA,QAAO;AAAA;AAAA,MAAmC;AACtD;AAAA,IACJ,KAAK;AACD,oBAAc,KAAK,OAAO,WAAW;AACrC;AAAA,IACJ,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,mBAAa,OAAO,KAAK,WAAW;AACpC,kBAAY;AAAA,QAAO;AAAA;AAAA,MAAmC;AACtD,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA+B;AAClD;AAAA,IACJ;AAAA,IACA,KAAK;AACD,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA6C;AAChE,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA+B;AAClD;AAAA,IACJ,KAAK;AACD,kBAAY;AAAA,QAAO;AAAA;AAAA,MAA6C;AAChE,kBAAY;AAAA,QAAO;AAAA;AAAA,MAAiC;AACpD;AAAA,EACR;AAEJ;AAEA,SAAS,UAAU,KAAK,UAAU,CAAC,GACjC;AACE,QAAM,cAAc,kBAAkB,GAAG;AACzC,cAAY;AAAA,IAAO;AAAA;AAAA,EAAyC;AAE5D,MAAI,QAAQ,aAAa,IAAI,MAAM,WAAW;AAE9C,QAAM,UAAU,YAAY,QAAQ;AACpC,MAAI,UAAU,MAAM,KAAK,QAAQ,OAAO;AAC5C;AAEA,SAAS,SAAS,KAAK;AACnB,QAAM,OAAO,IAAI;AACjB,MAAI,KAAK,SAAS,GAA2B;AACzC,wBAAoB,IAAI;AAAA,EAC5B,OACK;AACD,SAAK,MAAM,QAAQ,OAAK,oBAAoB,CAAC,CAAC;AAAA,EAClD;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,SAAS;AAClC,MAAI,QAAQ,MAAM,WAAW,GAAG;AAC5B,UAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,QAAI,KAAK,SAAS,KAA0B,KAAK,SAAS,GAA2B;AACjF,cAAQ,SAAS,KAAK;AACtB,aAAO,KAAK;AAAA,IAChB;AAAA,EACJ,OACK;AACD,UAAM,SAAS,CAAC;AAChB,aAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC3C,YAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,UAAI,EAAE,KAAK,SAAS,KAA0B,KAAK,SAAS,IAA4B;AACpF;AAAA,MACJ;AACA,UAAI,KAAK,SAAS,MAAM;AACpB;AAAA,MACJ;AACA,aAAO,KAAK,KAAK,KAAK;AAAA,IAC1B;AACA,QAAI,OAAO,WAAW,QAAQ,MAAM,QAAQ;AACxC,cAAQ,SAAS,KAAK,MAAM;AAC5B,eAAS,IAAI,GAAG,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC3C,cAAM,OAAO,QAAQ,MAAM,CAAC;AAC5B,YAAI,KAAK,SAAS,KAA0B,KAAK,SAAS,GAA2B;AACjF,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAEA,IAAM,iBAAiB;AAEvB,SAAS,OAAO,MAAM;AAClB,OAAK,IAAI,KAAK;AACd,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK,GAA4B;AAC7B,YAAM,WAAW;AACjB,aAAO,SAAS,IAAI;AACpB,eAAS,IAAI,SAAS;AACtB,aAAO,SAAS;AAChB;AAAA,IACJ;AAAA,IACA,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,YAAM,QAAQ,OAAO;AACrB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAO,MAAM,CAAC,CAAC;AAAA,MACnB;AACA,aAAO,IAAI;AACX,aAAO,OAAO;AACd;AAAA,IACJ;AAAA,IACA,KAAK,GAA2B;AAC5B,YAAM,UAAU;AAChB,YAAM,QAAQ,QAAQ;AACtB,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,eAAO,MAAM,CAAC,CAAC;AAAA,MACnB;AACA,cAAQ,IAAI;AACZ,aAAO,QAAQ;AACf,UAAI,QAAQ,QAAQ;AAChB,gBAAQ,IAAI,QAAQ;AACpB,eAAO,QAAQ;AAAA,MACnB;AACA;AAAA,IACJ;AAAA,IACA,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,GAA6B;AAC9B,YAAM,YAAY;AAClB,UAAI,UAAU,OAAO;AACjB,kBAAU,IAAI,UAAU;AACxB,eAAO,UAAU;AAAA,MACrB;AACA;AAAA,IACJ;AAAA,IACA,KAAK,GAA0B;AAC3B,YAAM,SAAS;AACf,aAAO,OAAO,GAAG;AACjB,aAAO,IAAI,OAAO;AAClB,aAAO,OAAO;AACd,UAAI,OAAO,UAAU;AACjB,eAAO,OAAO,QAAQ;AACtB,eAAO,IAAI,OAAO;AAClB,eAAO,OAAO;AAAA,MAClB;AACA;AAAA,IACJ;AAAA,IACA,KAAK,GAAwB;AACzB,YAAM,OAAO;AACb,WAAK,IAAI,KAAK;AACd,aAAO,KAAK;AACZ;AAAA,IACJ;AAAA,IACA,KAAK,GAAyB;AAC1B,YAAM,QAAQ;AACd,YAAM,IAAI,MAAM;AAChB,aAAO,MAAM;AACb;AAAA,IACJ;AAAA,IACA;AACI,UAAK,MAAwC;AACzC,cAAM,mBAAmB,kBAAkB,8BAA8B,MAAM;AAAA,UAC3E,QAAQ;AAAA,UACR,MAAM,CAAC,KAAK,IAAI;AAAA,QACpB,CAAC;AAAA,MACL;AAAA,EACR;AACA,SAAO,KAAK;AAChB;AAKA,IAAM,eAAe;AACrB,SAAS,oBAAoB,KAAK,SAAS;AACvC,QAAM,EAAE,WAAW,UAAU,eAAe,YAAY,YAAY,IAAI;AACxE,QAAM,WAAW,QAAQ,aAAa;AACtC,QAAM,WAAW;AAAA,IACb;AAAA,IACA,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,MAAM;AAAA,IACN,QAAQ;AAAA,IACR,KAAK;AAAA,IACL;AAAA,IACA,YAAY;AAAA,IACZ,aAAa;AAAA,EACjB;AACA,MAAI,YAAY,IAAI,KAAK;AACrB,aAAS,SAAS,IAAI,IAAI;AAAA,EAC9B;AACA,QAAM,UAAU,MAAM;AACtB,WAAS,KAAK,MAAM,MAAM;AACtB,aAAS,QAAQ;AAAA,EACrB;AACA,WAAS,SAAS,GAAG,gBAAgB,MAAM;AACvC,UAAM,iBAAiB,gBAAgB,gBAAgB;AACvD,SAAK,cAAc,iBAAiB,KAAK,OAAO,CAAC,IAAI,cAAc;AAAA,EACvE;AACA,WAAS,OAAO,cAAc,MAAM;AAChC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EACjC;AACA,WAAS,SAAS,cAAc,MAAM;AAClC,UAAM,QAAQ,EAAE,SAAS;AACzB,mBAAe,SAAS,KAAK;AAAA,EACjC;AACA,WAAS,UAAU;AACf,aAAS,SAAS,WAAW;AAAA,EACjC;AACA,QAAM,SAAS,CAAC,QAAQ,IAAI,GAAG;AAC/B,QAAM,aAAa,MAAM,SAAS;AAClC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;AACA,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,OAAO,IAAI;AACnB,YAAU,KAAK,GAAG;AAAA,IAAO;AAAA;AAAA,EAAmC,CAAC,GAAG;AAChE,eAAa,WAAW,KAAK,GAAG;AAChC,MAAI,KAAK,UAAU;AACf,cAAU,KAAK,IAAI;AACnB,iBAAa,WAAW,KAAK,QAAQ;AACrC,cAAU,KAAK,SAAS;AAAA,EAC5B,OACK;AACD,cAAU,KAAK,oBAAoB;AAAA,EACvC;AACA,YAAU,KAAK,GAAG;AACtB;AACA,SAAS,oBAAoB,WAAW,MAAM;AAC1C,QAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,YAAU,KAAK,GAAG;AAAA,IAAO;AAAA;AAAA,EAAyC,CAAC,IAAI;AACvE,YAAU,OAAO,WAAW,CAAC;AAC7B,QAAM,SAAS,KAAK,MAAM;AAC1B,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,iBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,QAAI,MAAM,SAAS,GAAG;AAClB;AAAA,IACJ;AACA,cAAU,KAAK,IAAI;AAAA,EACvB;AACA,YAAU,SAAS,WAAW,CAAC;AAC/B,YAAU,KAAK,IAAI;AACvB;AACA,SAAS,mBAAmB,WAAW,MAAM;AACzC,QAAM,EAAE,QAAQ,WAAW,IAAI;AAC/B,MAAI,KAAK,MAAM,SAAS,GAAG;AACvB,cAAU,KAAK,GAAG;AAAA,MAAO;AAAA;AAAA,IAAmC,CAAC,IAAI;AACjE,cAAU,OAAO,WAAW,CAAC;AAC7B,UAAM,SAAS,KAAK,MAAM;AAC1B,aAAS,IAAI,GAAG,IAAI,QAAQ,KAAK;AAC7B,mBAAa,WAAW,KAAK,MAAM,CAAC,CAAC;AACrC,UAAI,MAAM,SAAS,GAAG;AAClB;AAAA,MACJ;AACA,gBAAU,KAAK,IAAI;AAAA,IACvB;AACA,cAAU,SAAS,WAAW,CAAC;AAC/B,cAAU,KAAK,IAAI;AAAA,EACvB;AACJ;AACA,SAAS,iBAAiB,WAAW,MAAM;AACvC,MAAI,KAAK,MAAM;AACX,iBAAa,WAAW,KAAK,IAAI;AAAA,EACrC,OACK;AACD,cAAU,KAAK,MAAM;AAAA,EACzB;AACJ;AACA,SAAS,aAAa,WAAW,MAAM;AACnC,QAAM,EAAE,OAAO,IAAI;AACnB,UAAQ,KAAK,MAAM;AAAA,IACf,KAAK;AACD,uBAAiB,WAAW,IAAI;AAChC;AAAA,IACJ,KAAK;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,IACJ,KAAK;AACD,0BAAoB,WAAW,IAAI;AACnC;AAAA,IACJ,KAAK;AACD,yBAAmB,WAAW,IAAI;AAClC;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAA6C,CAAC,IAAI;AAAA,QAAO;AAAA;AAAA,MAA+B,CAAC,IAAI,KAAK,KAAK,MAAM,IAAI;AAC1I;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,GAAG;AAAA,QAAO;AAAA;AAAA,MAA6C,CAAC,IAAI;AAAA,QAAO;AAAA;AAAA,MAAiC,CAAC,IAAI,KAAK,UAAU,KAAK,GAAG,CAAC,MAAM,IAAI;AAC1J;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ,KAAK;AACD,gBAAU,KAAK,KAAK,UAAU,KAAK,KAAK,GAAG,IAAI;AAC/C;AAAA,IACJ;AACI,UAAK,MAAwC;AACzC,cAAM,mBAAmB,kBAAkB,6BAA6B,MAAM;AAAA,UAC1E,QAAQ;AAAA,UACR,MAAM,CAAC,KAAK,IAAI;AAAA,QACpB,CAAC;AAAA,MACL;AAAA,EACR;AACJ;AAEA,IAAM,WAAW,CAAC,KAAK,UAAU,CAAC,MAAM;AACpC,QAAM,OAAO,SAAS,QAAQ,IAAI,IAAI,QAAQ,OAAO;AACrD,QAAM,WAAW,SAAS,QAAQ,QAAQ,IACpC,QAAQ,WACR;AACN,QAAM,YAAY,CAAC,CAAC,QAAQ;AAE5B,QAAM,gBAAgB,QAAQ,iBAAiB,OACzC,QAAQ,gBACR,SAAS,UACL,MACA;AACV,QAAM,aAAa,QAAQ,aAAa,QAAQ,aAAa,SAAS;AACtE,QAAM,UAAU,IAAI,WAAW,CAAC;AAChC,QAAM,YAAY,oBAAoB,KAAK;AAAA,IACvC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ,CAAC;AACD,YAAU,KAAK,SAAS,WAAW,6BAA6B,YAAY;AAC5E,YAAU,OAAO,UAAU;AAC3B,MAAI,QAAQ,SAAS,GAAG;AACpB,cAAU,KAAK,WAAW,KAAK,QAAQ,IAAI,OAAK,GAAG,CAAC,MAAM,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU;AAC/E,cAAU,QAAQ;AAAA,EACtB;AACA,YAAU,KAAK,SAAS;AACxB,eAAa,WAAW,GAAG;AAC3B,YAAU,SAAS,UAAU;AAC7B,YAAU,KAAK,GAAG;AAClB,SAAO,IAAI;AACX,QAAM,EAAE,MAAM,IAAI,IAAI,UAAU,QAAQ;AACxC,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,KAAK,MAAM,IAAI,OAAO,IAAI;AAAA;AAAA,EAC9B;AACJ;AAEA,SAAS,YAAY,QAAQ,UAAU,CAAC,GAAG;AACvC,QAAM,kBAAkB,OAAO,CAAC,GAAG,OAAO;AAC1C,QAAM,MAAM,CAAC,CAAC,gBAAgB;AAC9B,QAAM,eAAe,CAAC,CAAC,gBAAgB;AACvC,QAAM,iBAAiB,gBAAgB,YAAY,OAAO,OAAO,gBAAgB;AAEjF,QAAM,SAAS,aAAa,eAAe;AAC3C,QAAM,MAAM,OAAO,MAAM,MAAM;AAC/B,MAAI,CAAC,KAAK;AAEN,cAAU,KAAK,eAAe;AAE9B,WAAO,SAAS,KAAK,eAAe;AAAA,EACxC,OACK;AAED,sBAAkB,SAAS,GAAG;AAE9B,oBAAgB,OAAO,GAAG;AAE1B,WAAO,EAAE,KAAK,MAAM,GAAG;AAAA,EAC3B;AACJ;", "names": ["index", "context"]}