{"version": 3, "sources": ["../../../../@intlify/shared/dist/shared.mjs"], "sourcesContent": ["/*!\n  * shared v11.1.11\n  * (c) 2025 ka<PERSON><PERSON> kawa<PERSON>\n  * Released under the MIT License.\n  */\nfunction warn(msg, err) {\n    if (typeof console !== 'undefined') {\n        console.warn(`[intlify] ` + msg);\n        /* istanbul ignore if */\n        if (err) {\n            console.warn(err.stack);\n        }\n    }\n}\nconst hasWarned = {};\nfunction warnOnce(msg) {\n    if (!hasWarned[msg]) {\n        hasWarned[msg] = true;\n        warn(msg);\n    }\n}\n\n/**\n * Original Utilities\n * written by kazuya kawaguchi\n */\nconst inBrowser = typeof window !== 'undefined';\nlet mark;\nlet measure;\nif ((process.env.NODE_ENV !== 'production')) {\n    const perf = inBrowser && window.performance;\n    if (perf &&\n        perf.mark &&\n        perf.measure &&\n        perf.clearMarks &&\n        // @ts-ignore browser compat\n        perf.clearMeasures) {\n        mark = (tag) => {\n            perf.mark(tag);\n        };\n        measure = (name, startTag, endTag) => {\n            perf.measure(name, startTag, endTag);\n            perf.clearMarks(startTag);\n            perf.clearMarks(endTag);\n        };\n    }\n}\nconst RE_ARGS = /\\{([0-9a-zA-Z]+)\\}/g;\n/* eslint-disable */\nfunction format(message, ...args) {\n    if (args.length === 1 && isObject(args[0])) {\n        args = args[0];\n    }\n    if (!args || !args.hasOwnProperty) {\n        args = {};\n    }\n    return message.replace(RE_ARGS, (match, identifier) => {\n        return args.hasOwnProperty(identifier) ? args[identifier] : '';\n    });\n}\nconst makeSymbol = (name, shareable = false) => !shareable ? Symbol(name) : Symbol.for(name);\nconst generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });\nconst friendlyJSONstringify = (json) => JSON.stringify(json)\n    .replace(/\\u2028/g, '\\\\u2028')\n    .replace(/\\u2029/g, '\\\\u2029')\n    .replace(/\\u0027/g, '\\\\u0027');\nconst isNumber = (val) => typeof val === 'number' && isFinite(val);\nconst isDate = (val) => toTypeString(val) === '[object Date]';\nconst isRegExp = (val) => toTypeString(val) === '[object RegExp]';\nconst isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;\nconst assign = Object.assign;\nconst _create = Object.create;\nconst create = (obj = null) => _create(obj);\nlet _globalThis;\nconst getGlobalThis = () => {\n    // prettier-ignore\n    return (_globalThis ||\n        (_globalThis =\n            typeof globalThis !== 'undefined'\n                ? globalThis\n                : typeof self !== 'undefined'\n                    ? self\n                    : typeof window !== 'undefined'\n                        ? window\n                        : typeof global !== 'undefined'\n                            ? global\n                            : create()));\n};\nfunction escapeHtml(rawText) {\n    return rawText\n        .replace(/&/g, '&amp;') // escape `&` first to avoid double escaping\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;')\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&apos;')\n        .replace(/\\//g, '&#x2F;') // escape `/` to prevent closing tags or JavaScript URLs\n        .replace(/=/g, '&#x3D;'); // escape `=` to prevent attribute injection\n}\nfunction escapeAttributeValue(value) {\n    return value\n        .replace(/&(?![a-zA-Z0-9#]{2,6};)/g, '&amp;') // escape unescaped `&`\n        .replace(/\"/g, '&quot;')\n        .replace(/'/g, '&apos;')\n        .replace(/</g, '&lt;')\n        .replace(/>/g, '&gt;');\n}\nfunction sanitizeTranslatedHtml(html) {\n    // Escape dangerous characters in attribute values\n    // Process attributes with double quotes\n    html = html.replace(/(\\w+)\\s*=\\s*\"([^\"]*)\"/g, (_, attrName, attrValue) => `${attrName}=\"${escapeAttributeValue(attrValue)}\"`);\n    // Process attributes with single quotes\n    html = html.replace(/(\\w+)\\s*=\\s*'([^']*)'/g, (_, attrName, attrValue) => `${attrName}='${escapeAttributeValue(attrValue)}'`);\n    // Detect and neutralize event handler attributes\n    const eventHandlerPattern = /\\s*on\\w+\\s*=\\s*[\"']?[^\"'>]+[\"']?/gi;\n    if (eventHandlerPattern.test(html)) {\n        if ((process.env.NODE_ENV !== 'production')) {\n            warn('Potentially dangerous event handlers detected in translation. ' +\n                'Consider removing onclick, onerror, etc. from your translation messages.');\n        }\n        // Neutralize event handler attributes by escaping 'on'\n        html = html.replace(/(\\s+)(on)(\\w+\\s*=)/gi, '$1&#111;n$3');\n    }\n    // Disable javascript: URLs in various contexts\n    const javascriptUrlPattern = [\n        // In href, src, action, formaction attributes\n        /(\\s+(?:href|src|action|formaction)\\s*=\\s*[\"']?)\\s*javascript:/gi,\n        // In style attributes within url()\n        /(style\\s*=\\s*[\"'][^\"']*url\\s*\\(\\s*)javascript:/gi\n    ];\n    javascriptUrlPattern.forEach(pattern => {\n        html = html.replace(pattern, '$1javascript&#58;');\n    });\n    return html;\n}\nconst hasOwnProperty = Object.prototype.hasOwnProperty;\nfunction hasOwn(obj, key) {\n    return hasOwnProperty.call(obj, key);\n}\n/* eslint-enable */\n/**\n * Useful Utilities By Evan you\n * Modified by kazuya kawaguchi\n * MIT License\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/index.ts\n * https://github.com/vuejs/vue-next/blob/master/packages/shared/src/codeframe.ts\n */\nconst isArray = Array.isArray;\nconst isFunction = (val) => typeof val === 'function';\nconst isString = (val) => typeof val === 'string';\nconst isBoolean = (val) => typeof val === 'boolean';\nconst isSymbol = (val) => typeof val === 'symbol';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isObject = (val) => val !== null && typeof val === 'object';\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst isPromise = (val) => {\n    return isObject(val) && isFunction(val.then) && isFunction(val.catch);\n};\nconst objectToString = Object.prototype.toString;\nconst toTypeString = (value) => objectToString.call(value);\nconst isPlainObject = (val) => toTypeString(val) === '[object Object]';\n// for converting list and named values to displayed strings.\nconst toDisplayString = (val) => {\n    return val == null\n        ? ''\n        : isArray(val) || (isPlainObject(val) && val.toString === objectToString)\n            ? JSON.stringify(val, null, 2)\n            : String(val);\n};\nfunction join(items, separator = '') {\n    return items.reduce((str, item, index) => (index === 0 ? str + item : str + separator + item), '');\n}\nconst RANGE = 2;\nfunction generateCodeFrame(source, start = 0, end = source.length) {\n    const lines = source.split(/\\r?\\n/);\n    let count = 0;\n    const res = [];\n    for (let i = 0; i < lines.length; i++) {\n        count += lines[i].length + 1;\n        if (count >= start) {\n            for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {\n                if (j < 0 || j >= lines.length)\n                    continue;\n                const line = j + 1;\n                res.push(`${line}${' '.repeat(3 - String(line).length)}|  ${lines[j]}`);\n                const lineLength = lines[j].length;\n                if (j === i) {\n                    // push underline\n                    const pad = start - (count - lineLength) + 1;\n                    const length = Math.max(1, end > count ? lineLength - pad : end - start);\n                    res.push(`   |  ` + ' '.repeat(pad) + '^'.repeat(length));\n                }\n                else if (j > i) {\n                    if (end > count) {\n                        const length = Math.max(Math.min(end - count, lineLength), 1);\n                        res.push(`   |  ` + '^'.repeat(length));\n                    }\n                    count += lineLength + 1;\n                }\n            }\n            break;\n        }\n    }\n    return res.join('\\n');\n}\n\n/**\n * Event emitter, forked from the below:\n * - original repository url: https://github.com/developit/mitt\n * - code url: https://github.com/developit/mitt/blob/master/src/index.ts\n * - author: Jason Miller (https://github.com/developit)\n * - license: MIT\n */\n/**\n * Create a event emitter\n *\n * @returns An event emitter\n */\nfunction createEmitter() {\n    const events = new Map();\n    const emitter = {\n        events,\n        on(event, handler) {\n            const handlers = events.get(event);\n            const added = handlers && handlers.push(handler);\n            if (!added) {\n                events.set(event, [handler]);\n            }\n        },\n        off(event, handler) {\n            const handlers = events.get(event);\n            if (handlers) {\n                handlers.splice(handlers.indexOf(handler) >>> 0, 1);\n            }\n        },\n        emit(event, payload) {\n            (events.get(event) || [])\n                .slice()\n                .map(handler => handler(payload));\n            (events.get('*') || [])\n                .slice()\n                .map(handler => handler(event, payload));\n        }\n    };\n    return emitter;\n}\n\nconst isNotObjectOrIsArray = (val) => !isObject(val) || isArray(val);\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nfunction deepCopy(src, des) {\n    // src and des should both be objects, and none of them can be a array\n    if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {\n        throw new Error('Invalid value');\n    }\n    const stack = [{ src, des }];\n    while (stack.length) {\n        const { src, des } = stack.pop();\n        // using `Object.keys` which skips prototype properties\n        Object.keys(src).forEach(key => {\n            if (key === '__proto__') {\n                return;\n            }\n            // if src[key] is an object/array, set des[key]\n            // to empty object/array to prevent setting by reference\n            if (isObject(src[key]) && !isObject(des[key])) {\n                des[key] = Array.isArray(src[key]) ? [] : create();\n            }\n            if (isNotObjectOrIsArray(des[key]) || isNotObjectOrIsArray(src[key])) {\n                // replace with src[key] when:\n                // src[key] or des[key] is not an object, or\n                // src[key] or des[key] is an array\n                des[key] = src[key];\n            }\n            else {\n                // src[key] and des[key] are both objects, merge them\n                stack.push({ src: src[key], des: des[key] });\n            }\n        });\n    }\n}\n\nexport { assign, create, createEmitter, deepCopy, escapeHtml, format, friendlyJSONstringify, generateCodeFrame, generateFormatCacheKey, getGlobalThis, hasOwn, inBrowser, isArray, isBoolean, isDate, isEmptyObject, isFunction, isNumber, isObject, isPlainObject, isPromise, isRegExp, isString, isSymbol, join, makeSymbol, mark, measure, objectToString, sanitizeTranslatedHtml, toDisplayString, toTypeString, warn, warnOnce };\n"], "mappings": ";AAKA,SAAS,KAAK,KAAK,KAAK;AACpB,MAAI,OAAO,YAAY,aAAa;AAChC,YAAQ,KAAK,eAAe,GAAG;AAE/B,QAAI,KAAK;AACL,cAAQ,KAAK,IAAI,KAAK;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,IAAM,YAAY,CAAC;AACnB,SAAS,SAAS,KAAK;AACnB,MAAI,CAAC,UAAU,GAAG,GAAG;AACjB,cAAU,GAAG,IAAI;AACjB,SAAK,GAAG;AAAA,EACZ;AACJ;AAMA,IAAM,YAAY,OAAO,WAAW;AACpC,IAAI;AACJ,IAAI;AACJ,IAAK,MAAwC;AACzC,QAAM,OAAO,aAAa,OAAO;AACjC,MAAI,QACA,KAAK,QACL,KAAK,WACL,KAAK;AAAA,EAEL,KAAK,eAAe;AACpB,WAAO,CAAC,QAAQ;AACZ,WAAK,KAAK,GAAG;AAAA,IACjB;AACA,cAAU,CAAC,MAAM,UAAU,WAAW;AAClC,WAAK,QAAQ,MAAM,UAAU,MAAM;AACnC,WAAK,WAAW,QAAQ;AACxB,WAAK,WAAW,MAAM;AAAA,IAC1B;AAAA,EACJ;AACJ;AACA,IAAM,UAAU;AAEhB,SAAS,OAAO,YAAY,MAAM;AAC9B,MAAI,KAAK,WAAW,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG;AACxC,WAAO,KAAK,CAAC;AAAA,EACjB;AACA,MAAI,CAAC,QAAQ,CAAC,KAAK,gBAAgB;AAC/B,WAAO,CAAC;AAAA,EACZ;AACA,SAAO,QAAQ,QAAQ,SAAS,CAAC,OAAO,eAAe;AACnD,WAAO,KAAK,eAAe,UAAU,IAAI,KAAK,UAAU,IAAI;AAAA,EAChE,CAAC;AACL;AACA,IAAM,aAAa,CAAC,MAAM,YAAY,UAAU,CAAC,YAAY,OAAO,IAAI,IAAI,OAAO,IAAI,IAAI;AAC3F,IAAM,yBAAyB,CAAC,QAAQ,KAAK,WAAW,sBAAsB,EAAE,GAAG,QAAQ,GAAG,KAAK,GAAG,OAAO,CAAC;AAC9G,IAAM,wBAAwB,CAAC,SAAS,KAAK,UAAU,IAAI,EACtD,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS,EAC5B,QAAQ,WAAW,SAAS;AACjC,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ,YAAY,SAAS,GAAG;AACjE,IAAM,SAAS,CAAC,QAAQ,aAAa,GAAG,MAAM;AAC9C,IAAM,WAAW,CAAC,QAAQ,aAAa,GAAG,MAAM;AAChD,IAAM,gBAAgB,CAAC,QAAQ,cAAc,GAAG,KAAK,OAAO,KAAK,GAAG,EAAE,WAAW;AACjF,IAAM,SAAS,OAAO;AACtB,IAAM,UAAU,OAAO;AACvB,IAAM,SAAS,CAAC,MAAM,SAAS,QAAQ,GAAG;AAC1C,IAAI;AACJ,IAAM,gBAAgB,MAAM;AAExB,SAAQ,gBACH,cACG,OAAO,eAAe,cAChB,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,OAAO;AACrC;AACA,SAAS,WAAW,SAAS;AACzB,SAAO,QACF,QAAQ,MAAM,OAAO,EACrB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,OAAO,QAAQ,EACvB,QAAQ,MAAM,QAAQ;AAC/B;AACA,SAAS,qBAAqB,OAAO;AACjC,SAAO,MACF,QAAQ,4BAA4B,OAAO,EAC3C,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,QAAQ,EACtB,QAAQ,MAAM,MAAM,EACpB,QAAQ,MAAM,MAAM;AAC7B;AACA,SAAS,uBAAuB,MAAM;AAGlC,SAAO,KAAK,QAAQ,0BAA0B,CAAC,GAAG,UAAU,cAAc,GAAG,QAAQ,KAAK,qBAAqB,SAAS,CAAC,GAAG;AAE5H,SAAO,KAAK,QAAQ,0BAA0B,CAAC,GAAG,UAAU,cAAc,GAAG,QAAQ,KAAK,qBAAqB,SAAS,CAAC,GAAG;AAE5H,QAAM,sBAAsB;AAC5B,MAAI,oBAAoB,KAAK,IAAI,GAAG;AAChC,QAAK,MAAwC;AACzC,WAAK,wIACyE;AAAA,IAClF;AAEA,WAAO,KAAK,QAAQ,wBAAwB,aAAa;AAAA,EAC7D;AAEA,QAAM,uBAAuB;AAAA;AAAA,IAEzB;AAAA;AAAA,IAEA;AAAA,EACJ;AACA,uBAAqB,QAAQ,aAAW;AACpC,WAAO,KAAK,QAAQ,SAAS,mBAAmB;AAAA,EACpD,CAAC;AACD,SAAO;AACX;AACA,IAAM,iBAAiB,OAAO,UAAU;AACxC,SAAS,OAAO,KAAK,KAAK;AACtB,SAAO,eAAe,KAAK,KAAK,GAAG;AACvC;AASA,IAAM,UAAU,MAAM;AACtB,IAAM,aAAa,CAAC,QAAQ,OAAO,QAAQ;AAC3C,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AACzC,IAAM,YAAY,CAAC,QAAQ,OAAO,QAAQ;AAC1C,IAAM,WAAW,CAAC,QAAQ,OAAO,QAAQ;AAEzC,IAAM,WAAW,CAAC,QAAQ,QAAQ,QAAQ,OAAO,QAAQ;AAEzD,IAAM,YAAY,CAAC,QAAQ;AACvB,SAAO,SAAS,GAAG,KAAK,WAAW,IAAI,IAAI,KAAK,WAAW,IAAI,KAAK;AACxE;AACA,IAAM,iBAAiB,OAAO,UAAU;AACxC,IAAM,eAAe,CAAC,UAAU,eAAe,KAAK,KAAK;AACzD,IAAM,gBAAgB,CAAC,QAAQ,aAAa,GAAG,MAAM;AAErD,IAAM,kBAAkB,CAAC,QAAQ;AAC7B,SAAO,OAAO,OACR,KACA,QAAQ,GAAG,KAAM,cAAc,GAAG,KAAK,IAAI,aAAa,iBACpD,KAAK,UAAU,KAAK,MAAM,CAAC,IAC3B,OAAO,GAAG;AACxB;AACA,SAAS,KAAK,OAAO,YAAY,IAAI;AACjC,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,UAAW,UAAU,IAAI,MAAM,OAAO,MAAM,YAAY,MAAO,EAAE;AACrG;AACA,IAAM,QAAQ;AACd,SAAS,kBAAkB,QAAQ,QAAQ,GAAG,MAAM,OAAO,QAAQ;AAC/D,QAAM,QAAQ,OAAO,MAAM,OAAO;AAClC,MAAI,QAAQ;AACZ,QAAM,MAAM,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,aAAS,MAAM,CAAC,EAAE,SAAS;AAC3B,QAAI,SAAS,OAAO;AAChB,eAAS,IAAI,IAAI,OAAO,KAAK,IAAI,SAAS,MAAM,OAAO,KAAK;AACxD,YAAI,IAAI,KAAK,KAAK,MAAM;AACpB;AACJ,cAAM,OAAO,IAAI;AACjB,YAAI,KAAK,GAAG,IAAI,GAAG,IAAI,OAAO,IAAI,OAAO,IAAI,EAAE,MAAM,CAAC,MAAM,MAAM,CAAC,CAAC,EAAE;AACtE,cAAM,aAAa,MAAM,CAAC,EAAE;AAC5B,YAAI,MAAM,GAAG;AAET,gBAAM,MAAM,SAAS,QAAQ,cAAc;AAC3C,gBAAM,SAAS,KAAK,IAAI,GAAG,MAAM,QAAQ,aAAa,MAAM,MAAM,KAAK;AACvE,cAAI,KAAK,WAAW,IAAI,OAAO,GAAG,IAAI,IAAI,OAAO,MAAM,CAAC;AAAA,QAC5D,WACS,IAAI,GAAG;AACZ,cAAI,MAAM,OAAO;AACb,kBAAM,SAAS,KAAK,IAAI,KAAK,IAAI,MAAM,OAAO,UAAU,GAAG,CAAC;AAC5D,gBAAI,KAAK,WAAW,IAAI,OAAO,MAAM,CAAC;AAAA,UAC1C;AACA,mBAAS,aAAa;AAAA,QAC1B;AAAA,MACJ;AACA;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,IAAI,KAAK,IAAI;AACxB;AAcA,SAAS,gBAAgB;AACrB,QAAM,SAAS,oBAAI,IAAI;AACvB,QAAM,UAAU;AAAA,IACZ;AAAA,IACA,GAAG,OAAO,SAAS;AACf,YAAM,WAAW,OAAO,IAAI,KAAK;AACjC,YAAM,QAAQ,YAAY,SAAS,KAAK,OAAO;AAC/C,UAAI,CAAC,OAAO;AACR,eAAO,IAAI,OAAO,CAAC,OAAO,CAAC;AAAA,MAC/B;AAAA,IACJ;AAAA,IACA,IAAI,OAAO,SAAS;AAChB,YAAM,WAAW,OAAO,IAAI,KAAK;AACjC,UAAI,UAAU;AACV,iBAAS,OAAO,SAAS,QAAQ,OAAO,MAAM,GAAG,CAAC;AAAA,MACtD;AAAA,IACJ;AAAA,IACA,KAAK,OAAO,SAAS;AACjB,OAAC,OAAO,IAAI,KAAK,KAAK,CAAC,GAClB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,CAAC;AACpC,OAAC,OAAO,IAAI,GAAG,KAAK,CAAC,GAChB,MAAM,EACN,IAAI,aAAW,QAAQ,OAAO,OAAO,CAAC;AAAA,IAC/C;AAAA,EACJ;AACA,SAAO;AACX;AAEA,IAAM,uBAAuB,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,QAAQ,GAAG;AAEnE,SAAS,SAAS,KAAK,KAAK;AAExB,MAAI,qBAAqB,GAAG,KAAK,qBAAqB,GAAG,GAAG;AACxD,UAAM,IAAI,MAAM,eAAe;AAAA,EACnC;AACA,QAAM,QAAQ,CAAC,EAAE,KAAK,IAAI,CAAC;AAC3B,SAAO,MAAM,QAAQ;AACjB,UAAM,EAAE,KAAAA,MAAK,KAAAC,KAAI,IAAI,MAAM,IAAI;AAE/B,WAAO,KAAKD,IAAG,EAAE,QAAQ,SAAO;AAC5B,UAAI,QAAQ,aAAa;AACrB;AAAA,MACJ;AAGA,UAAI,SAASA,KAAI,GAAG,CAAC,KAAK,CAAC,SAASC,KAAI,GAAG,CAAC,GAAG;AAC3C,QAAAA,KAAI,GAAG,IAAI,MAAM,QAAQD,KAAI,GAAG,CAAC,IAAI,CAAC,IAAI,OAAO;AAAA,MACrD;AACA,UAAI,qBAAqBC,KAAI,GAAG,CAAC,KAAK,qBAAqBD,KAAI,GAAG,CAAC,GAAG;AAIlE,QAAAC,KAAI,GAAG,IAAID,KAAI,GAAG;AAAA,MACtB,OACK;AAED,cAAM,KAAK,EAAE,KAAKA,KAAI,GAAG,GAAG,KAAKC,KAAI,GAAG,EAAE,CAAC;AAAA,MAC/C;AAAA,IACJ,CAAC;AAAA,EACL;AACJ;", "names": ["src", "des"]}