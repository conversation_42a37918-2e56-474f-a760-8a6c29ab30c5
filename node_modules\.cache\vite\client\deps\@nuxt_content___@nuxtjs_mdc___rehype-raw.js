import {
  esm_default,
  pointEnd,
  pointStart
} from "./chunk-4GTXHKNR.js";
import {
  <PERSON>rser,
  TokenizerMode,
  html_exports,
  token_exports
} from "./chunk-ITUG525O.js";
import {
  zwitch
} from "./chunk-L3M7MDWL.js";
import {
  visit
} from "./chunk-A7GY76GT.js";
import {
  ok
} from "./chunk-434LCRC6.js";
import "./chunk-5YG5I5XG.js";
import {
  __export
} from "./chunk-G3PMV62Z.js";

// node_modules/property-information/lib/util/schema.js
var Schema = class {
  /**
   * @param {SchemaType['property']} property
   *   Property.
   * @param {SchemaType['normal']} normal
   *   Normal.
   * @param {Space | undefined} [space]
   *   Space.
   * @returns
   *   Schema.
   */
  constructor(property, normal, space) {
    this.normal = normal;
    this.property = property;
    if (space) {
      this.space = space;
    }
  }
};
Schema.prototype.normal = {};
Schema.prototype.property = {};
Schema.prototype.space = void 0;

// node_modules/property-information/lib/util/merge.js
function merge(definitions, space) {
  const property = {};
  const normal = {};
  for (const definition of definitions) {
    Object.assign(property, definition.property);
    Object.assign(normal, definition.normal);
  }
  return new Schema(property, normal, space);
}

// node_modules/property-information/lib/normalize.js
function normalize(value) {
  return value.toLowerCase();
}

// node_modules/property-information/lib/util/info.js
var Info = class {
  /**
   * @param {string} property
   *   Property.
   * @param {string} attribute
   *   Attribute.
   * @returns
   *   Info.
   */
  constructor(property, attribute) {
    this.attribute = attribute;
    this.property = property;
  }
};
Info.prototype.attribute = "";
Info.prototype.booleanish = false;
Info.prototype.boolean = false;
Info.prototype.commaOrSpaceSeparated = false;
Info.prototype.commaSeparated = false;
Info.prototype.defined = false;
Info.prototype.mustUseProperty = false;
Info.prototype.number = false;
Info.prototype.overloadedBoolean = false;
Info.prototype.property = "";
Info.prototype.spaceSeparated = false;
Info.prototype.space = void 0;

// node_modules/property-information/lib/util/types.js
var types_exports = {};
__export(types_exports, {
  boolean: () => boolean,
  booleanish: () => booleanish,
  commaOrSpaceSeparated: () => commaOrSpaceSeparated,
  commaSeparated: () => commaSeparated,
  number: () => number,
  overloadedBoolean: () => overloadedBoolean,
  spaceSeparated: () => spaceSeparated
});
var powers = 0;
var boolean = increment();
var booleanish = increment();
var overloadedBoolean = increment();
var number = increment();
var spaceSeparated = increment();
var commaSeparated = increment();
var commaOrSpaceSeparated = increment();
function increment() {
  return 2 ** ++powers;
}

// node_modules/property-information/lib/util/defined-info.js
var checks = (
  /** @type {ReadonlyArray<keyof typeof types>} */
  Object.keys(types_exports)
);
var DefinedInfo = class extends Info {
  /**
   * @constructor
   * @param {string} property
   *   Property.
   * @param {string} attribute
   *   Attribute.
   * @param {number | null | undefined} [mask]
   *   Mask.
   * @param {Space | undefined} [space]
   *   Space.
   * @returns
   *   Info.
   */
  constructor(property, attribute, mask, space) {
    let index = -1;
    super(property, attribute);
    mark(this, "space", space);
    if (typeof mask === "number") {
      while (++index < checks.length) {
        const check = checks[index];
        mark(this, checks[index], (mask & types_exports[check]) === types_exports[check]);
      }
    }
  }
};
DefinedInfo.prototype.defined = true;
function mark(values, key, value) {
  if (value) {
    values[key] = value;
  }
}

// node_modules/property-information/lib/util/create.js
function create(definition) {
  const properties = {};
  const normals = {};
  for (const [property, value] of Object.entries(definition.properties)) {
    const info = new DefinedInfo(
      property,
      definition.transform(definition.attributes || {}, property),
      value,
      definition.space
    );
    if (definition.mustUseProperty && definition.mustUseProperty.includes(property)) {
      info.mustUseProperty = true;
    }
    properties[property] = info;
    normals[normalize(property)] = property;
    normals[normalize(info.attribute)] = property;
  }
  return new Schema(properties, normals, definition.space);
}

// node_modules/property-information/lib/aria.js
var aria = create({
  properties: {
    ariaActiveDescendant: null,
    ariaAtomic: booleanish,
    ariaAutoComplete: null,
    ariaBusy: booleanish,
    ariaChecked: booleanish,
    ariaColCount: number,
    ariaColIndex: number,
    ariaColSpan: number,
    ariaControls: spaceSeparated,
    ariaCurrent: null,
    ariaDescribedBy: spaceSeparated,
    ariaDetails: null,
    ariaDisabled: booleanish,
    ariaDropEffect: spaceSeparated,
    ariaErrorMessage: null,
    ariaExpanded: booleanish,
    ariaFlowTo: spaceSeparated,
    ariaGrabbed: booleanish,
    ariaHasPopup: null,
    ariaHidden: booleanish,
    ariaInvalid: null,
    ariaKeyShortcuts: null,
    ariaLabel: null,
    ariaLabelledBy: spaceSeparated,
    ariaLevel: number,
    ariaLive: null,
    ariaModal: booleanish,
    ariaMultiLine: booleanish,
    ariaMultiSelectable: booleanish,
    ariaOrientation: null,
    ariaOwns: spaceSeparated,
    ariaPlaceholder: null,
    ariaPosInSet: number,
    ariaPressed: booleanish,
    ariaReadOnly: booleanish,
    ariaRelevant: null,
    ariaRequired: booleanish,
    ariaRoleDescription: spaceSeparated,
    ariaRowCount: number,
    ariaRowIndex: number,
    ariaRowSpan: number,
    ariaSelected: booleanish,
    ariaSetSize: number,
    ariaSort: null,
    ariaValueMax: number,
    ariaValueMin: number,
    ariaValueNow: number,
    ariaValueText: null,
    role: null
  },
  transform(_, property) {
    return property === "role" ? property : "aria-" + property.slice(4).toLowerCase();
  }
});

// node_modules/property-information/lib/util/case-sensitive-transform.js
function caseSensitiveTransform(attributes, attribute) {
  return attribute in attributes ? attributes[attribute] : attribute;
}

// node_modules/property-information/lib/util/case-insensitive-transform.js
function caseInsensitiveTransform(attributes, property) {
  return caseSensitiveTransform(attributes, property.toLowerCase());
}

// node_modules/property-information/lib/html.js
var html = create({
  attributes: {
    acceptcharset: "accept-charset",
    classname: "class",
    htmlfor: "for",
    httpequiv: "http-equiv"
  },
  mustUseProperty: ["checked", "multiple", "muted", "selected"],
  properties: {
    // Standard Properties.
    abbr: null,
    accept: commaSeparated,
    acceptCharset: spaceSeparated,
    accessKey: spaceSeparated,
    action: null,
    allow: null,
    allowFullScreen: boolean,
    allowPaymentRequest: boolean,
    allowUserMedia: boolean,
    alt: null,
    as: null,
    async: boolean,
    autoCapitalize: null,
    autoComplete: spaceSeparated,
    autoFocus: boolean,
    autoPlay: boolean,
    blocking: spaceSeparated,
    capture: null,
    charSet: null,
    checked: boolean,
    cite: null,
    className: spaceSeparated,
    cols: number,
    colSpan: null,
    content: null,
    contentEditable: booleanish,
    controls: boolean,
    controlsList: spaceSeparated,
    coords: number | commaSeparated,
    crossOrigin: null,
    data: null,
    dateTime: null,
    decoding: null,
    default: boolean,
    defer: boolean,
    dir: null,
    dirName: null,
    disabled: boolean,
    download: overloadedBoolean,
    draggable: booleanish,
    encType: null,
    enterKeyHint: null,
    fetchPriority: null,
    form: null,
    formAction: null,
    formEncType: null,
    formMethod: null,
    formNoValidate: boolean,
    formTarget: null,
    headers: spaceSeparated,
    height: number,
    hidden: overloadedBoolean,
    high: number,
    href: null,
    hrefLang: null,
    htmlFor: spaceSeparated,
    httpEquiv: spaceSeparated,
    id: null,
    imageSizes: null,
    imageSrcSet: null,
    inert: boolean,
    inputMode: null,
    integrity: null,
    is: null,
    isMap: boolean,
    itemId: null,
    itemProp: spaceSeparated,
    itemRef: spaceSeparated,
    itemScope: boolean,
    itemType: spaceSeparated,
    kind: null,
    label: null,
    lang: null,
    language: null,
    list: null,
    loading: null,
    loop: boolean,
    low: number,
    manifest: null,
    max: null,
    maxLength: number,
    media: null,
    method: null,
    min: null,
    minLength: number,
    multiple: boolean,
    muted: boolean,
    name: null,
    nonce: null,
    noModule: boolean,
    noValidate: boolean,
    onAbort: null,
    onAfterPrint: null,
    onAuxClick: null,
    onBeforeMatch: null,
    onBeforePrint: null,
    onBeforeToggle: null,
    onBeforeUnload: null,
    onBlur: null,
    onCancel: null,
    onCanPlay: null,
    onCanPlayThrough: null,
    onChange: null,
    onClick: null,
    onClose: null,
    onContextLost: null,
    onContextMenu: null,
    onContextRestored: null,
    onCopy: null,
    onCueChange: null,
    onCut: null,
    onDblClick: null,
    onDrag: null,
    onDragEnd: null,
    onDragEnter: null,
    onDragExit: null,
    onDragLeave: null,
    onDragOver: null,
    onDragStart: null,
    onDrop: null,
    onDurationChange: null,
    onEmptied: null,
    onEnded: null,
    onError: null,
    onFocus: null,
    onFormData: null,
    onHashChange: null,
    onInput: null,
    onInvalid: null,
    onKeyDown: null,
    onKeyPress: null,
    onKeyUp: null,
    onLanguageChange: null,
    onLoad: null,
    onLoadedData: null,
    onLoadedMetadata: null,
    onLoadEnd: null,
    onLoadStart: null,
    onMessage: null,
    onMessageError: null,
    onMouseDown: null,
    onMouseEnter: null,
    onMouseLeave: null,
    onMouseMove: null,
    onMouseOut: null,
    onMouseOver: null,
    onMouseUp: null,
    onOffline: null,
    onOnline: null,
    onPageHide: null,
    onPageShow: null,
    onPaste: null,
    onPause: null,
    onPlay: null,
    onPlaying: null,
    onPopState: null,
    onProgress: null,
    onRateChange: null,
    onRejectionHandled: null,
    onReset: null,
    onResize: null,
    onScroll: null,
    onScrollEnd: null,
    onSecurityPolicyViolation: null,
    onSeeked: null,
    onSeeking: null,
    onSelect: null,
    onSlotChange: null,
    onStalled: null,
    onStorage: null,
    onSubmit: null,
    onSuspend: null,
    onTimeUpdate: null,
    onToggle: null,
    onUnhandledRejection: null,
    onUnload: null,
    onVolumeChange: null,
    onWaiting: null,
    onWheel: null,
    open: boolean,
    optimum: number,
    pattern: null,
    ping: spaceSeparated,
    placeholder: null,
    playsInline: boolean,
    popover: null,
    popoverTarget: null,
    popoverTargetAction: null,
    poster: null,
    preload: null,
    readOnly: boolean,
    referrerPolicy: null,
    rel: spaceSeparated,
    required: boolean,
    reversed: boolean,
    rows: number,
    rowSpan: number,
    sandbox: spaceSeparated,
    scope: null,
    scoped: boolean,
    seamless: boolean,
    selected: boolean,
    shadowRootClonable: boolean,
    shadowRootDelegatesFocus: boolean,
    shadowRootMode: null,
    shape: null,
    size: number,
    sizes: null,
    slot: null,
    span: number,
    spellCheck: booleanish,
    src: null,
    srcDoc: null,
    srcLang: null,
    srcSet: null,
    start: number,
    step: null,
    style: null,
    tabIndex: number,
    target: null,
    title: null,
    translate: null,
    type: null,
    typeMustMatch: boolean,
    useMap: null,
    value: booleanish,
    width: number,
    wrap: null,
    writingSuggestions: null,
    // Legacy.
    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis
    align: null,
    // Several. Use CSS `text-align` instead,
    aLink: null,
    // `<body>`. Use CSS `a:active {color}` instead
    archive: spaceSeparated,
    // `<object>`. List of URIs to archives
    axis: null,
    // `<td>` and `<th>`. Use `scope` on `<th>`
    background: null,
    // `<body>`. Use CSS `background-image` instead
    bgColor: null,
    // `<body>` and table elements. Use CSS `background-color` instead
    border: number,
    // `<table>`. Use CSS `border-width` instead,
    borderColor: null,
    // `<table>`. Use CSS `border-color` instead,
    bottomMargin: number,
    // `<body>`
    cellPadding: null,
    // `<table>`
    cellSpacing: null,
    // `<table>`
    char: null,
    // Several table elements. When `align=char`, sets the character to align on
    charOff: null,
    // Several table elements. When `char`, offsets the alignment
    classId: null,
    // `<object>`
    clear: null,
    // `<br>`. Use CSS `clear` instead
    code: null,
    // `<object>`
    codeBase: null,
    // `<object>`
    codeType: null,
    // `<object>`
    color: null,
    // `<font>` and `<hr>`. Use CSS instead
    compact: boolean,
    // Lists. Use CSS to reduce space between items instead
    declare: boolean,
    // `<object>`
    event: null,
    // `<script>`
    face: null,
    // `<font>`. Use CSS instead
    frame: null,
    // `<table>`
    frameBorder: null,
    // `<iframe>`. Use CSS `border` instead
    hSpace: number,
    // `<img>` and `<object>`
    leftMargin: number,
    // `<body>`
    link: null,
    // `<body>`. Use CSS `a:link {color: *}` instead
    longDesc: null,
    // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`
    lowSrc: null,
    // `<img>`. Use a `<picture>`
    marginHeight: number,
    // `<body>`
    marginWidth: number,
    // `<body>`
    noResize: boolean,
    // `<frame>`
    noHref: boolean,
    // `<area>`. Use no href instead of an explicit `nohref`
    noShade: boolean,
    // `<hr>`. Use background-color and height instead of borders
    noWrap: boolean,
    // `<td>` and `<th>`
    object: null,
    // `<applet>`
    profile: null,
    // `<head>`
    prompt: null,
    // `<isindex>`
    rev: null,
    // `<link>`
    rightMargin: number,
    // `<body>`
    rules: null,
    // `<table>`
    scheme: null,
    // `<meta>`
    scrolling: booleanish,
    // `<frame>`. Use overflow in the child context
    standby: null,
    // `<object>`
    summary: null,
    // `<table>`
    text: null,
    // `<body>`. Use CSS `color` instead
    topMargin: number,
    // `<body>`
    valueType: null,
    // `<param>`
    version: null,
    // `<html>`. Use a doctype.
    vAlign: null,
    // Several. Use CSS `vertical-align` instead
    vLink: null,
    // `<body>`. Use CSS `a:visited {color}` instead
    vSpace: number,
    // `<img>` and `<object>`
    // Non-standard Properties.
    allowTransparency: null,
    autoCorrect: null,
    autoSave: null,
    disablePictureInPicture: boolean,
    disableRemotePlayback: boolean,
    prefix: null,
    property: null,
    results: number,
    security: null,
    unselectable: null
  },
  space: "html",
  transform: caseInsensitiveTransform
});

// node_modules/property-information/lib/svg.js
var svg = create({
  attributes: {
    accentHeight: "accent-height",
    alignmentBaseline: "alignment-baseline",
    arabicForm: "arabic-form",
    baselineShift: "baseline-shift",
    capHeight: "cap-height",
    className: "class",
    clipPath: "clip-path",
    clipRule: "clip-rule",
    colorInterpolation: "color-interpolation",
    colorInterpolationFilters: "color-interpolation-filters",
    colorProfile: "color-profile",
    colorRendering: "color-rendering",
    crossOrigin: "crossorigin",
    dataType: "datatype",
    dominantBaseline: "dominant-baseline",
    enableBackground: "enable-background",
    fillOpacity: "fill-opacity",
    fillRule: "fill-rule",
    floodColor: "flood-color",
    floodOpacity: "flood-opacity",
    fontFamily: "font-family",
    fontSize: "font-size",
    fontSizeAdjust: "font-size-adjust",
    fontStretch: "font-stretch",
    fontStyle: "font-style",
    fontVariant: "font-variant",
    fontWeight: "font-weight",
    glyphName: "glyph-name",
    glyphOrientationHorizontal: "glyph-orientation-horizontal",
    glyphOrientationVertical: "glyph-orientation-vertical",
    hrefLang: "hreflang",
    horizAdvX: "horiz-adv-x",
    horizOriginX: "horiz-origin-x",
    horizOriginY: "horiz-origin-y",
    imageRendering: "image-rendering",
    letterSpacing: "letter-spacing",
    lightingColor: "lighting-color",
    markerEnd: "marker-end",
    markerMid: "marker-mid",
    markerStart: "marker-start",
    navDown: "nav-down",
    navDownLeft: "nav-down-left",
    navDownRight: "nav-down-right",
    navLeft: "nav-left",
    navNext: "nav-next",
    navPrev: "nav-prev",
    navRight: "nav-right",
    navUp: "nav-up",
    navUpLeft: "nav-up-left",
    navUpRight: "nav-up-right",
    onAbort: "onabort",
    onActivate: "onactivate",
    onAfterPrint: "onafterprint",
    onBeforePrint: "onbeforeprint",
    onBegin: "onbegin",
    onCancel: "oncancel",
    onCanPlay: "oncanplay",
    onCanPlayThrough: "oncanplaythrough",
    onChange: "onchange",
    onClick: "onclick",
    onClose: "onclose",
    onCopy: "oncopy",
    onCueChange: "oncuechange",
    onCut: "oncut",
    onDblClick: "ondblclick",
    onDrag: "ondrag",
    onDragEnd: "ondragend",
    onDragEnter: "ondragenter",
    onDragExit: "ondragexit",
    onDragLeave: "ondragleave",
    onDragOver: "ondragover",
    onDragStart: "ondragstart",
    onDrop: "ondrop",
    onDurationChange: "ondurationchange",
    onEmptied: "onemptied",
    onEnd: "onend",
    onEnded: "onended",
    onError: "onerror",
    onFocus: "onfocus",
    onFocusIn: "onfocusin",
    onFocusOut: "onfocusout",
    onHashChange: "onhashchange",
    onInput: "oninput",
    onInvalid: "oninvalid",
    onKeyDown: "onkeydown",
    onKeyPress: "onkeypress",
    onKeyUp: "onkeyup",
    onLoad: "onload",
    onLoadedData: "onloadeddata",
    onLoadedMetadata: "onloadedmetadata",
    onLoadStart: "onloadstart",
    onMessage: "onmessage",
    onMouseDown: "onmousedown",
    onMouseEnter: "onmouseenter",
    onMouseLeave: "onmouseleave",
    onMouseMove: "onmousemove",
    onMouseOut: "onmouseout",
    onMouseOver: "onmouseover",
    onMouseUp: "onmouseup",
    onMouseWheel: "onmousewheel",
    onOffline: "onoffline",
    onOnline: "ononline",
    onPageHide: "onpagehide",
    onPageShow: "onpageshow",
    onPaste: "onpaste",
    onPause: "onpause",
    onPlay: "onplay",
    onPlaying: "onplaying",
    onPopState: "onpopstate",
    onProgress: "onprogress",
    onRateChange: "onratechange",
    onRepeat: "onrepeat",
    onReset: "onreset",
    onResize: "onresize",
    onScroll: "onscroll",
    onSeeked: "onseeked",
    onSeeking: "onseeking",
    onSelect: "onselect",
    onShow: "onshow",
    onStalled: "onstalled",
    onStorage: "onstorage",
    onSubmit: "onsubmit",
    onSuspend: "onsuspend",
    onTimeUpdate: "ontimeupdate",
    onToggle: "ontoggle",
    onUnload: "onunload",
    onVolumeChange: "onvolumechange",
    onWaiting: "onwaiting",
    onZoom: "onzoom",
    overlinePosition: "overline-position",
    overlineThickness: "overline-thickness",
    paintOrder: "paint-order",
    panose1: "panose-1",
    pointerEvents: "pointer-events",
    referrerPolicy: "referrerpolicy",
    renderingIntent: "rendering-intent",
    shapeRendering: "shape-rendering",
    stopColor: "stop-color",
    stopOpacity: "stop-opacity",
    strikethroughPosition: "strikethrough-position",
    strikethroughThickness: "strikethrough-thickness",
    strokeDashArray: "stroke-dasharray",
    strokeDashOffset: "stroke-dashoffset",
    strokeLineCap: "stroke-linecap",
    strokeLineJoin: "stroke-linejoin",
    strokeMiterLimit: "stroke-miterlimit",
    strokeOpacity: "stroke-opacity",
    strokeWidth: "stroke-width",
    tabIndex: "tabindex",
    textAnchor: "text-anchor",
    textDecoration: "text-decoration",
    textRendering: "text-rendering",
    transformOrigin: "transform-origin",
    typeOf: "typeof",
    underlinePosition: "underline-position",
    underlineThickness: "underline-thickness",
    unicodeBidi: "unicode-bidi",
    unicodeRange: "unicode-range",
    unitsPerEm: "units-per-em",
    vAlphabetic: "v-alphabetic",
    vHanging: "v-hanging",
    vIdeographic: "v-ideographic",
    vMathematical: "v-mathematical",
    vectorEffect: "vector-effect",
    vertAdvY: "vert-adv-y",
    vertOriginX: "vert-origin-x",
    vertOriginY: "vert-origin-y",
    wordSpacing: "word-spacing",
    writingMode: "writing-mode",
    xHeight: "x-height",
    // These were camelcased in Tiny. Now lowercased in SVG 2
    playbackOrder: "playbackorder",
    timelineBegin: "timelinebegin"
  },
  properties: {
    about: commaOrSpaceSeparated,
    accentHeight: number,
    accumulate: null,
    additive: null,
    alignmentBaseline: null,
    alphabetic: number,
    amplitude: number,
    arabicForm: null,
    ascent: number,
    attributeName: null,
    attributeType: null,
    azimuth: number,
    bandwidth: null,
    baselineShift: null,
    baseFrequency: null,
    baseProfile: null,
    bbox: null,
    begin: null,
    bias: number,
    by: null,
    calcMode: null,
    capHeight: number,
    className: spaceSeparated,
    clip: null,
    clipPath: null,
    clipPathUnits: null,
    clipRule: null,
    color: null,
    colorInterpolation: null,
    colorInterpolationFilters: null,
    colorProfile: null,
    colorRendering: null,
    content: null,
    contentScriptType: null,
    contentStyleType: null,
    crossOrigin: null,
    cursor: null,
    cx: null,
    cy: null,
    d: null,
    dataType: null,
    defaultAction: null,
    descent: number,
    diffuseConstant: number,
    direction: null,
    display: null,
    dur: null,
    divisor: number,
    dominantBaseline: null,
    download: boolean,
    dx: null,
    dy: null,
    edgeMode: null,
    editable: null,
    elevation: number,
    enableBackground: null,
    end: null,
    event: null,
    exponent: number,
    externalResourcesRequired: null,
    fill: null,
    fillOpacity: number,
    fillRule: null,
    filter: null,
    filterRes: null,
    filterUnits: null,
    floodColor: null,
    floodOpacity: null,
    focusable: null,
    focusHighlight: null,
    fontFamily: null,
    fontSize: null,
    fontSizeAdjust: null,
    fontStretch: null,
    fontStyle: null,
    fontVariant: null,
    fontWeight: null,
    format: null,
    fr: null,
    from: null,
    fx: null,
    fy: null,
    g1: commaSeparated,
    g2: commaSeparated,
    glyphName: commaSeparated,
    glyphOrientationHorizontal: null,
    glyphOrientationVertical: null,
    glyphRef: null,
    gradientTransform: null,
    gradientUnits: null,
    handler: null,
    hanging: number,
    hatchContentUnits: null,
    hatchUnits: null,
    height: null,
    href: null,
    hrefLang: null,
    horizAdvX: number,
    horizOriginX: number,
    horizOriginY: number,
    id: null,
    ideographic: number,
    imageRendering: null,
    initialVisibility: null,
    in: null,
    in2: null,
    intercept: number,
    k: number,
    k1: number,
    k2: number,
    k3: number,
    k4: number,
    kernelMatrix: commaOrSpaceSeparated,
    kernelUnitLength: null,
    keyPoints: null,
    // SEMI_COLON_SEPARATED
    keySplines: null,
    // SEMI_COLON_SEPARATED
    keyTimes: null,
    // SEMI_COLON_SEPARATED
    kerning: null,
    lang: null,
    lengthAdjust: null,
    letterSpacing: null,
    lightingColor: null,
    limitingConeAngle: number,
    local: null,
    markerEnd: null,
    markerMid: null,
    markerStart: null,
    markerHeight: null,
    markerUnits: null,
    markerWidth: null,
    mask: null,
    maskContentUnits: null,
    maskUnits: null,
    mathematical: null,
    max: null,
    media: null,
    mediaCharacterEncoding: null,
    mediaContentEncodings: null,
    mediaSize: number,
    mediaTime: null,
    method: null,
    min: null,
    mode: null,
    name: null,
    navDown: null,
    navDownLeft: null,
    navDownRight: null,
    navLeft: null,
    navNext: null,
    navPrev: null,
    navRight: null,
    navUp: null,
    navUpLeft: null,
    navUpRight: null,
    numOctaves: null,
    observer: null,
    offset: null,
    onAbort: null,
    onActivate: null,
    onAfterPrint: null,
    onBeforePrint: null,
    onBegin: null,
    onCancel: null,
    onCanPlay: null,
    onCanPlayThrough: null,
    onChange: null,
    onClick: null,
    onClose: null,
    onCopy: null,
    onCueChange: null,
    onCut: null,
    onDblClick: null,
    onDrag: null,
    onDragEnd: null,
    onDragEnter: null,
    onDragExit: null,
    onDragLeave: null,
    onDragOver: null,
    onDragStart: null,
    onDrop: null,
    onDurationChange: null,
    onEmptied: null,
    onEnd: null,
    onEnded: null,
    onError: null,
    onFocus: null,
    onFocusIn: null,
    onFocusOut: null,
    onHashChange: null,
    onInput: null,
    onInvalid: null,
    onKeyDown: null,
    onKeyPress: null,
    onKeyUp: null,
    onLoad: null,
    onLoadedData: null,
    onLoadedMetadata: null,
    onLoadStart: null,
    onMessage: null,
    onMouseDown: null,
    onMouseEnter: null,
    onMouseLeave: null,
    onMouseMove: null,
    onMouseOut: null,
    onMouseOver: null,
    onMouseUp: null,
    onMouseWheel: null,
    onOffline: null,
    onOnline: null,
    onPageHide: null,
    onPageShow: null,
    onPaste: null,
    onPause: null,
    onPlay: null,
    onPlaying: null,
    onPopState: null,
    onProgress: null,
    onRateChange: null,
    onRepeat: null,
    onReset: null,
    onResize: null,
    onScroll: null,
    onSeeked: null,
    onSeeking: null,
    onSelect: null,
    onShow: null,
    onStalled: null,
    onStorage: null,
    onSubmit: null,
    onSuspend: null,
    onTimeUpdate: null,
    onToggle: null,
    onUnload: null,
    onVolumeChange: null,
    onWaiting: null,
    onZoom: null,
    opacity: null,
    operator: null,
    order: null,
    orient: null,
    orientation: null,
    origin: null,
    overflow: null,
    overlay: null,
    overlinePosition: number,
    overlineThickness: number,
    paintOrder: null,
    panose1: null,
    path: null,
    pathLength: number,
    patternContentUnits: null,
    patternTransform: null,
    patternUnits: null,
    phase: null,
    ping: spaceSeparated,
    pitch: null,
    playbackOrder: null,
    pointerEvents: null,
    points: null,
    pointsAtX: number,
    pointsAtY: number,
    pointsAtZ: number,
    preserveAlpha: null,
    preserveAspectRatio: null,
    primitiveUnits: null,
    propagate: null,
    property: commaOrSpaceSeparated,
    r: null,
    radius: null,
    referrerPolicy: null,
    refX: null,
    refY: null,
    rel: commaOrSpaceSeparated,
    rev: commaOrSpaceSeparated,
    renderingIntent: null,
    repeatCount: null,
    repeatDur: null,
    requiredExtensions: commaOrSpaceSeparated,
    requiredFeatures: commaOrSpaceSeparated,
    requiredFonts: commaOrSpaceSeparated,
    requiredFormats: commaOrSpaceSeparated,
    resource: null,
    restart: null,
    result: null,
    rotate: null,
    rx: null,
    ry: null,
    scale: null,
    seed: null,
    shapeRendering: null,
    side: null,
    slope: null,
    snapshotTime: null,
    specularConstant: number,
    specularExponent: number,
    spreadMethod: null,
    spacing: null,
    startOffset: null,
    stdDeviation: null,
    stemh: null,
    stemv: null,
    stitchTiles: null,
    stopColor: null,
    stopOpacity: null,
    strikethroughPosition: number,
    strikethroughThickness: number,
    string: null,
    stroke: null,
    strokeDashArray: commaOrSpaceSeparated,
    strokeDashOffset: null,
    strokeLineCap: null,
    strokeLineJoin: null,
    strokeMiterLimit: number,
    strokeOpacity: number,
    strokeWidth: null,
    style: null,
    surfaceScale: number,
    syncBehavior: null,
    syncBehaviorDefault: null,
    syncMaster: null,
    syncTolerance: null,
    syncToleranceDefault: null,
    systemLanguage: commaOrSpaceSeparated,
    tabIndex: number,
    tableValues: null,
    target: null,
    targetX: number,
    targetY: number,
    textAnchor: null,
    textDecoration: null,
    textRendering: null,
    textLength: null,
    timelineBegin: null,
    title: null,
    transformBehavior: null,
    type: null,
    typeOf: commaOrSpaceSeparated,
    to: null,
    transform: null,
    transformOrigin: null,
    u1: null,
    u2: null,
    underlinePosition: number,
    underlineThickness: number,
    unicode: null,
    unicodeBidi: null,
    unicodeRange: null,
    unitsPerEm: number,
    values: null,
    vAlphabetic: number,
    vMathematical: number,
    vectorEffect: null,
    vHanging: number,
    vIdeographic: number,
    version: null,
    vertAdvY: number,
    vertOriginX: number,
    vertOriginY: number,
    viewBox: null,
    viewTarget: null,
    visibility: null,
    width: null,
    widths: null,
    wordSpacing: null,
    writingMode: null,
    x: null,
    x1: null,
    x2: null,
    xChannelSelector: null,
    xHeight: number,
    y: null,
    y1: null,
    y2: null,
    yChannelSelector: null,
    z: null,
    zoomAndPan: null
  },
  space: "svg",
  transform: caseSensitiveTransform
});

// node_modules/property-information/lib/xlink.js
var xlink = create({
  properties: {
    xLinkActuate: null,
    xLinkArcRole: null,
    xLinkHref: null,
    xLinkRole: null,
    xLinkShow: null,
    xLinkTitle: null,
    xLinkType: null
  },
  space: "xlink",
  transform(_, property) {
    return "xlink:" + property.slice(5).toLowerCase();
  }
});

// node_modules/property-information/lib/xmlns.js
var xmlns = create({
  attributes: { xmlnsxlink: "xmlns:xlink" },
  properties: { xmlnsXLink: null, xmlns: null },
  space: "xmlns",
  transform: caseInsensitiveTransform
});

// node_modules/property-information/lib/xml.js
var xml = create({
  properties: { xmlBase: null, xmlLang: null, xmlSpace: null },
  space: "xml",
  transform(_, property) {
    return "xml:" + property.slice(3).toLowerCase();
  }
});

// node_modules/property-information/lib/find.js
var cap = /[A-Z]/g;
var dash = /-[a-z]/g;
var valid = /^data[-\w.:]+$/i;
function find(schema, value) {
  const normal = normalize(value);
  let property = value;
  let Type = Info;
  if (normal in schema.normal) {
    return schema.property[schema.normal[normal]];
  }
  if (normal.length > 4 && normal.slice(0, 4) === "data" && valid.test(value)) {
    if (value.charAt(4) === "-") {
      const rest = value.slice(5).replace(dash, camelcase);
      property = "data" + rest.charAt(0).toUpperCase() + rest.slice(1);
    } else {
      const rest = value.slice(4);
      if (!dash.test(rest)) {
        let dashes = rest.replace(cap, kebab);
        if (dashes.charAt(0) !== "-") {
          dashes = "-" + dashes;
        }
        value = "data" + dashes;
      }
    }
    Type = DefinedInfo;
  }
  return new Type(property, value);
}
function kebab($0) {
  return "-" + $0.toLowerCase();
}
function camelcase($0) {
  return $0.charAt(1).toUpperCase();
}

// node_modules/property-information/index.js
var html2 = merge([aria, html, xlink, xmlns, xml], "html");
var svg2 = merge([aria, svg, xlink, xmlns, xml], "svg");

// node_modules/comma-separated-tokens/index.js
function parse(value) {
  const tokens = [];
  const input = String(value || "");
  let index = input.indexOf(",");
  let start = 0;
  let end = false;
  while (!end) {
    if (index === -1) {
      index = input.length;
      end = true;
    }
    const token = input.slice(start, index).trim();
    if (token || !end) {
      tokens.push(token);
    }
    start = index + 1;
    index = input.indexOf(",", start);
  }
  return tokens;
}
function stringify(values, options) {
  const settings = options || {};
  const input = values[values.length - 1] === "" ? [...values, ""] : values;
  return input.join(
    (settings.padRight ? " " : "") + "," + (settings.padLeft === false ? "" : " ")
  ).trim();
}

// node_modules/hast-util-parse-selector/lib/index.js
var search = /[#.]/g;
function parseSelector(selector, defaultTagName) {
  const value = selector || "";
  const props = {};
  let start = 0;
  let previous;
  let tagName;
  while (start < value.length) {
    search.lastIndex = start;
    const match = search.exec(value);
    const subvalue = value.slice(start, match ? match.index : value.length);
    if (subvalue) {
      if (!previous) {
        tagName = subvalue;
      } else if (previous === "#") {
        props.id = subvalue;
      } else if (Array.isArray(props.className)) {
        props.className.push(subvalue);
      } else {
        props.className = [subvalue];
      }
      start += subvalue.length;
    }
    if (match) {
      previous = match[0];
      start++;
    }
  }
  return {
    type: "element",
    // @ts-expect-error: tag name is parsed.
    tagName: tagName || defaultTagName || "div",
    properties: props,
    children: []
  };
}

// node_modules/space-separated-tokens/index.js
function parse2(value) {
  const input = String(value || "").trim();
  return input ? input.split(/[ \t\n\r\f]+/g) : [];
}
function stringify2(values) {
  return values.join(" ").trim();
}

// node_modules/hastscript/lib/create-h.js
function createH(schema, defaultTagName, caseSensitive) {
  const adjust = caseSensitive ? createAdjustMap(caseSensitive) : void 0;
  function h2(selector, properties, ...children) {
    let node;
    if (selector === null || selector === void 0) {
      node = { type: "root", children: [] };
      const child = (
        /** @type {Child} */
        properties
      );
      children.unshift(child);
    } else {
      node = parseSelector(selector, defaultTagName);
      const lower = node.tagName.toLowerCase();
      const adjusted = adjust ? adjust.get(lower) : void 0;
      node.tagName = adjusted || lower;
      if (isChild(properties)) {
        children.unshift(properties);
      } else {
        for (const [key, value] of Object.entries(properties)) {
          addProperty(schema, node.properties, key, value);
        }
      }
    }
    for (const child of children) {
      addChild(node.children, child);
    }
    if (node.type === "element" && node.tagName === "template") {
      node.content = { type: "root", children: node.children };
      node.children = [];
    }
    return node;
  }
  return h2;
}
function isChild(value) {
  if (value === null || typeof value !== "object" || Array.isArray(value)) {
    return true;
  }
  if (typeof value.type !== "string") return false;
  const record = (
    /** @type {Record<string, unknown>} */
    value
  );
  const keys = Object.keys(value);
  for (const key of keys) {
    const value2 = record[key];
    if (value2 && typeof value2 === "object") {
      if (!Array.isArray(value2)) return true;
      const list = (
        /** @type {ReadonlyArray<unknown>} */
        value2
      );
      for (const item of list) {
        if (typeof item !== "number" && typeof item !== "string") {
          return true;
        }
      }
    }
  }
  if ("children" in value && Array.isArray(value.children)) {
    return true;
  }
  return false;
}
function addProperty(schema, properties, key, value) {
  const info = find(schema, key);
  let result;
  if (value === null || value === void 0) return;
  if (typeof value === "number") {
    if (Number.isNaN(value)) return;
    result = value;
  } else if (typeof value === "boolean") {
    result = value;
  } else if (typeof value === "string") {
    if (info.spaceSeparated) {
      result = parse2(value);
    } else if (info.commaSeparated) {
      result = parse(value);
    } else if (info.commaOrSpaceSeparated) {
      result = parse2(parse(value).join(" "));
    } else {
      result = parsePrimitive(info, info.property, value);
    }
  } else if (Array.isArray(value)) {
    result = [...value];
  } else {
    result = info.property === "style" ? style(value) : String(value);
  }
  if (Array.isArray(result)) {
    const finalResult = [];
    for (const item of result) {
      finalResult.push(
        /** @type {number | string} */
        parsePrimitive(info, info.property, item)
      );
    }
    result = finalResult;
  }
  if (info.property === "className" && Array.isArray(properties.className)) {
    result = properties.className.concat(
      /** @type {Array<number | string> | number | string} */
      result
    );
  }
  properties[info.property] = result;
}
function addChild(nodes, value) {
  if (value === null || value === void 0) {
  } else if (typeof value === "number" || typeof value === "string") {
    nodes.push({ type: "text", value: String(value) });
  } else if (Array.isArray(value)) {
    for (const child of value) {
      addChild(nodes, child);
    }
  } else if (typeof value === "object" && "type" in value) {
    if (value.type === "root") {
      addChild(nodes, value.children);
    } else {
      nodes.push(value);
    }
  } else {
    throw new Error("Expected node, nodes, or string, got `" + value + "`");
  }
}
function parsePrimitive(info, name, value) {
  if (typeof value === "string") {
    if (info.number && value && !Number.isNaN(Number(value))) {
      return Number(value);
    }
    if ((info.boolean || info.overloadedBoolean) && (value === "" || normalize(value) === normalize(name))) {
      return true;
    }
  }
  return value;
}
function style(styles) {
  const result = [];
  for (const [key, value] of Object.entries(styles)) {
    result.push([key, value].join(": "));
  }
  return result.join("; ");
}
function createAdjustMap(values) {
  const result = /* @__PURE__ */ new Map();
  for (const value of values) {
    result.set(value.toLowerCase(), value);
  }
  return result;
}

// node_modules/hastscript/lib/svg-case-sensitive-tag-names.js
var svgCaseSensitiveTagNames = [
  "altGlyph",
  "altGlyphDef",
  "altGlyphItem",
  "animateColor",
  "animateMotion",
  "animateTransform",
  "clipPath",
  "feBlend",
  "feColorMatrix",
  "feComponentTransfer",
  "feComposite",
  "feConvolveMatrix",
  "feDiffuseLighting",
  "feDisplacementMap",
  "feDistantLight",
  "feDropShadow",
  "feFlood",
  "feFuncA",
  "feFuncB",
  "feFuncG",
  "feFuncR",
  "feGaussianBlur",
  "feImage",
  "feMerge",
  "feMergeNode",
  "feMorphology",
  "feOffset",
  "fePointLight",
  "feSpecularLighting",
  "feSpotLight",
  "feTile",
  "feTurbulence",
  "foreignObject",
  "glyphRef",
  "linearGradient",
  "radialGradient",
  "solidColor",
  "textArea",
  "textPath"
];

// node_modules/hastscript/lib/index.js
var h = createH(html2, "div");
var s = createH(svg2, "g", svgCaseSensitiveTagNames);

// node_modules/vfile-location/lib/index.js
function location(file) {
  const value = String(file);
  const indices = [];
  return { toOffset, toPoint };
  function toPoint(offset) {
    if (typeof offset === "number" && offset > -1 && offset <= value.length) {
      let index = 0;
      while (true) {
        let end = indices[index];
        if (end === void 0) {
          const eol = next(value, indices[index - 1]);
          end = eol === -1 ? value.length + 1 : eol + 1;
          indices[index] = end;
        }
        if (end > offset) {
          return {
            line: index + 1,
            column: offset - (index > 0 ? indices[index - 1] : 0) + 1,
            offset
          };
        }
        index++;
      }
    }
  }
  function toOffset(point2) {
    if (point2 && typeof point2.line === "number" && typeof point2.column === "number" && !Number.isNaN(point2.line) && !Number.isNaN(point2.column)) {
      while (indices.length < point2.line) {
        const from = indices[indices.length - 1];
        const eol = next(value, from);
        const end = eol === -1 ? value.length + 1 : eol + 1;
        if (from === end) break;
        indices.push(end);
      }
      const offset = (point2.line > 1 ? indices[point2.line - 2] : 0) + point2.column - 1;
      if (offset < indices[point2.line - 1]) return offset;
    }
  }
}
function next(value, from) {
  const cr = value.indexOf("\r", from);
  const lf = value.indexOf("\n", from);
  if (lf === -1) return cr;
  if (cr === -1 || cr + 1 === lf) return lf;
  return cr < lf ? cr : lf;
}

// node_modules/web-namespaces/index.js
var webNamespaces = {
  html: "http://www.w3.org/1999/xhtml",
  mathml: "http://www.w3.org/1998/Math/MathML",
  svg: "http://www.w3.org/2000/svg",
  xlink: "http://www.w3.org/1999/xlink",
  xml: "http://www.w3.org/XML/1998/namespace",
  xmlns: "http://www.w3.org/2000/xmlns/"
};

// node_modules/hast-util-from-parse5/lib/index.js
var own = {}.hasOwnProperty;
var proto = Object.prototype;
function fromParse5(tree, options) {
  const settings = options || {};
  return one(
    {
      file: settings.file || void 0,
      location: false,
      schema: settings.space === "svg" ? svg2 : html2,
      verbose: settings.verbose || false
    },
    tree
  );
}
function one(state, node) {
  let result;
  switch (node.nodeName) {
    case "#comment": {
      const reference = (
        /** @type {DefaultTreeAdapterMap['commentNode']} */
        node
      );
      result = { type: "comment", value: reference.data };
      patch(state, reference, result);
      return result;
    }
    case "#document":
    case "#document-fragment": {
      const reference = (
        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */
        node
      );
      const quirksMode = "mode" in reference ? reference.mode === "quirks" || reference.mode === "limited-quirks" : false;
      result = {
        type: "root",
        children: all(state, node.childNodes),
        data: { quirksMode }
      };
      if (state.file && state.location) {
        const document = String(state.file);
        const loc = location(document);
        const start = loc.toPoint(0);
        const end = loc.toPoint(document.length);
        ok(start, "expected `start`");
        ok(end, "expected `end`");
        result.position = { start, end };
      }
      return result;
    }
    case "#documentType": {
      const reference = (
        /** @type {DefaultTreeAdapterMap['documentType']} */
        node
      );
      result = { type: "doctype" };
      patch(state, reference, result);
      return result;
    }
    case "#text": {
      const reference = (
        /** @type {DefaultTreeAdapterMap['textNode']} */
        node
      );
      result = { type: "text", value: reference.value };
      patch(state, reference, result);
      return result;
    }
    // Element.
    default: {
      const reference = (
        /** @type {DefaultTreeAdapterMap['element']} */
        node
      );
      result = element(state, reference);
      return result;
    }
  }
}
function all(state, nodes) {
  let index = -1;
  const results = [];
  while (++index < nodes.length) {
    const result = (
      /** @type {RootContent} */
      one(state, nodes[index])
    );
    results.push(result);
  }
  return results;
}
function element(state, node) {
  const schema = state.schema;
  state.schema = node.namespaceURI === webNamespaces.svg ? svg2 : html2;
  let index = -1;
  const properties = {};
  while (++index < node.attrs.length) {
    const attribute = node.attrs[index];
    const name = (attribute.prefix ? attribute.prefix + ":" : "") + attribute.name;
    if (!own.call(proto, name)) {
      properties[name] = attribute.value;
    }
  }
  const x = state.schema.space === "svg" ? s : h;
  const result = x(node.tagName, properties, all(state, node.childNodes));
  patch(state, node, result);
  if (result.tagName === "template") {
    const reference = (
      /** @type {DefaultTreeAdapterMap['template']} */
      node
    );
    const pos = reference.sourceCodeLocation;
    const startTag2 = pos && pos.startTag && position(pos.startTag);
    const endTag2 = pos && pos.endTag && position(pos.endTag);
    const content = (
      /** @type {Root} */
      one(state, reference.content)
    );
    if (startTag2 && endTag2 && state.file) {
      content.position = { start: startTag2.end, end: endTag2.start };
    }
    result.content = content;
  }
  state.schema = schema;
  return result;
}
function patch(state, from, to) {
  if ("sourceCodeLocation" in from && from.sourceCodeLocation && state.file) {
    const position2 = createLocation(state, to, from.sourceCodeLocation);
    if (position2) {
      state.location = true;
      to.position = position2;
    }
  }
}
function createLocation(state, node, location2) {
  const result = position(location2);
  if (node.type === "element") {
    const tail = node.children[node.children.length - 1];
    if (result && !location2.endTag && tail && tail.position && tail.position.end) {
      result.end = Object.assign({}, tail.position.end);
    }
    if (state.verbose) {
      const properties = {};
      let key;
      if (location2.attrs) {
        for (key in location2.attrs) {
          if (own.call(location2.attrs, key)) {
            properties[find(state.schema, key).property] = position(
              location2.attrs[key]
            );
          }
        }
      }
      ok(location2.startTag, "a start tag should exist");
      const opening = position(location2.startTag);
      const closing = location2.endTag ? position(location2.endTag) : void 0;
      const data = { opening };
      if (closing) data.closing = closing;
      data.properties = properties;
      node.data = { position: data };
    }
  }
  return result;
}
function position(loc) {
  const start = point({
    line: loc.startLine,
    column: loc.startCol,
    offset: loc.startOffset
  });
  const end = point({
    line: loc.endLine,
    column: loc.endCol,
    offset: loc.endOffset
  });
  return start || end ? { start, end } : void 0;
}
function point(point2) {
  return point2.line && point2.column ? point2 : void 0;
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/schema.js
var Schema2 = class {
  /**
   * @constructor
   * @param {Properties} property
   * @param {Normal} normal
   * @param {string} [space]
   */
  constructor(property, normal, space) {
    this.property = property;
    this.normal = normal;
    if (space) {
      this.space = space;
    }
  }
};
Schema2.prototype.property = {};
Schema2.prototype.normal = {};
Schema2.prototype.space = null;

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/merge.js
function merge2(definitions, space) {
  const property = {};
  const normal = {};
  let index = -1;
  while (++index < definitions.length) {
    Object.assign(property, definitions[index].property);
    Object.assign(normal, definitions[index].normal);
  }
  return new Schema2(property, normal, space);
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/normalize.js
function normalize2(value) {
  return value.toLowerCase();
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/info.js
var Info2 = class {
  /**
   * @constructor
   * @param {string} property
   * @param {string} attribute
   */
  constructor(property, attribute) {
    this.property = property;
    this.attribute = attribute;
  }
};
Info2.prototype.space = null;
Info2.prototype.boolean = false;
Info2.prototype.booleanish = false;
Info2.prototype.overloadedBoolean = false;
Info2.prototype.number = false;
Info2.prototype.commaSeparated = false;
Info2.prototype.spaceSeparated = false;
Info2.prototype.commaOrSpaceSeparated = false;
Info2.prototype.mustUseProperty = false;
Info2.prototype.defined = false;

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/types.js
var types_exports2 = {};
__export(types_exports2, {
  boolean: () => boolean2,
  booleanish: () => booleanish2,
  commaOrSpaceSeparated: () => commaOrSpaceSeparated2,
  commaSeparated: () => commaSeparated2,
  number: () => number2,
  overloadedBoolean: () => overloadedBoolean2,
  spaceSeparated: () => spaceSeparated2
});
var powers2 = 0;
var boolean2 = increment2();
var booleanish2 = increment2();
var overloadedBoolean2 = increment2();
var number2 = increment2();
var spaceSeparated2 = increment2();
var commaSeparated2 = increment2();
var commaOrSpaceSeparated2 = increment2();
function increment2() {
  return 2 ** ++powers2;
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/defined-info.js
var checks2 = Object.keys(types_exports2);
var DefinedInfo2 = class extends Info2 {
  /**
   * @constructor
   * @param {string} property
   * @param {string} attribute
   * @param {number|null} [mask]
   * @param {string} [space]
   */
  constructor(property, attribute, mask, space) {
    let index = -1;
    super(property, attribute);
    mark2(this, "space", space);
    if (typeof mask === "number") {
      while (++index < checks2.length) {
        const check = checks2[index];
        mark2(this, checks2[index], (mask & types_exports2[check]) === types_exports2[check]);
      }
    }
  }
};
DefinedInfo2.prototype.defined = true;
function mark2(values, key, value) {
  if (value) {
    values[key] = value;
  }
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/create.js
var own2 = {}.hasOwnProperty;
function create2(definition) {
  const property = {};
  const normal = {};
  let prop;
  for (prop in definition.properties) {
    if (own2.call(definition.properties, prop)) {
      const value = definition.properties[prop];
      const info = new DefinedInfo2(
        prop,
        definition.transform(definition.attributes || {}, prop),
        value,
        definition.space
      );
      if (definition.mustUseProperty && definition.mustUseProperty.includes(prop)) {
        info.mustUseProperty = true;
      }
      property[prop] = info;
      normal[normalize2(prop)] = prop;
      normal[normalize2(info.attribute)] = prop;
    }
  }
  return new Schema2(property, normal, definition.space);
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/xlink.js
var xlink2 = create2({
  space: "xlink",
  transform(_, prop) {
    return "xlink:" + prop.slice(5).toLowerCase();
  },
  properties: {
    xLinkActuate: null,
    xLinkArcRole: null,
    xLinkHref: null,
    xLinkRole: null,
    xLinkShow: null,
    xLinkTitle: null,
    xLinkType: null
  }
});

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/xml.js
var xml2 = create2({
  space: "xml",
  transform(_, prop) {
    return "xml:" + prop.slice(3).toLowerCase();
  },
  properties: { xmlLang: null, xmlBase: null, xmlSpace: null }
});

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/case-sensitive-transform.js
function caseSensitiveTransform2(attributes, attribute) {
  return attribute in attributes ? attributes[attribute] : attribute;
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/util/case-insensitive-transform.js
function caseInsensitiveTransform2(attributes, property) {
  return caseSensitiveTransform2(attributes, property.toLowerCase());
}

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/xmlns.js
var xmlns2 = create2({
  space: "xmlns",
  attributes: { xmlnsxlink: "xmlns:xlink" },
  transform: caseInsensitiveTransform2,
  properties: { xmlns: null, xmlnsXLink: null }
});

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/aria.js
var aria2 = create2({
  transform(_, prop) {
    return prop === "role" ? prop : "aria-" + prop.slice(4).toLowerCase();
  },
  properties: {
    ariaActiveDescendant: null,
    ariaAtomic: booleanish2,
    ariaAutoComplete: null,
    ariaBusy: booleanish2,
    ariaChecked: booleanish2,
    ariaColCount: number2,
    ariaColIndex: number2,
    ariaColSpan: number2,
    ariaControls: spaceSeparated2,
    ariaCurrent: null,
    ariaDescribedBy: spaceSeparated2,
    ariaDetails: null,
    ariaDisabled: booleanish2,
    ariaDropEffect: spaceSeparated2,
    ariaErrorMessage: null,
    ariaExpanded: booleanish2,
    ariaFlowTo: spaceSeparated2,
    ariaGrabbed: booleanish2,
    ariaHasPopup: null,
    ariaHidden: booleanish2,
    ariaInvalid: null,
    ariaKeyShortcuts: null,
    ariaLabel: null,
    ariaLabelledBy: spaceSeparated2,
    ariaLevel: number2,
    ariaLive: null,
    ariaModal: booleanish2,
    ariaMultiLine: booleanish2,
    ariaMultiSelectable: booleanish2,
    ariaOrientation: null,
    ariaOwns: spaceSeparated2,
    ariaPlaceholder: null,
    ariaPosInSet: number2,
    ariaPressed: booleanish2,
    ariaReadOnly: booleanish2,
    ariaRelevant: null,
    ariaRequired: booleanish2,
    ariaRoleDescription: spaceSeparated2,
    ariaRowCount: number2,
    ariaRowIndex: number2,
    ariaRowSpan: number2,
    ariaSelected: booleanish2,
    ariaSetSize: number2,
    ariaSort: null,
    ariaValueMax: number2,
    ariaValueMin: number2,
    ariaValueNow: number2,
    ariaValueText: null,
    role: null
  }
});

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/html.js
var html3 = create2({
  space: "html",
  attributes: {
    acceptcharset: "accept-charset",
    classname: "class",
    htmlfor: "for",
    httpequiv: "http-equiv"
  },
  transform: caseInsensitiveTransform2,
  mustUseProperty: ["checked", "multiple", "muted", "selected"],
  properties: {
    // Standard Properties.
    abbr: null,
    accept: commaSeparated2,
    acceptCharset: spaceSeparated2,
    accessKey: spaceSeparated2,
    action: null,
    allow: null,
    allowFullScreen: boolean2,
    allowPaymentRequest: boolean2,
    allowUserMedia: boolean2,
    alt: null,
    as: null,
    async: boolean2,
    autoCapitalize: null,
    autoComplete: spaceSeparated2,
    autoFocus: boolean2,
    autoPlay: boolean2,
    blocking: spaceSeparated2,
    capture: null,
    charSet: null,
    checked: boolean2,
    cite: null,
    className: spaceSeparated2,
    cols: number2,
    colSpan: null,
    content: null,
    contentEditable: booleanish2,
    controls: boolean2,
    controlsList: spaceSeparated2,
    coords: number2 | commaSeparated2,
    crossOrigin: null,
    data: null,
    dateTime: null,
    decoding: null,
    default: boolean2,
    defer: boolean2,
    dir: null,
    dirName: null,
    disabled: boolean2,
    download: overloadedBoolean2,
    draggable: booleanish2,
    encType: null,
    enterKeyHint: null,
    fetchPriority: null,
    form: null,
    formAction: null,
    formEncType: null,
    formMethod: null,
    formNoValidate: boolean2,
    formTarget: null,
    headers: spaceSeparated2,
    height: number2,
    hidden: boolean2,
    high: number2,
    href: null,
    hrefLang: null,
    htmlFor: spaceSeparated2,
    httpEquiv: spaceSeparated2,
    id: null,
    imageSizes: null,
    imageSrcSet: null,
    inert: boolean2,
    inputMode: null,
    integrity: null,
    is: null,
    isMap: boolean2,
    itemId: null,
    itemProp: spaceSeparated2,
    itemRef: spaceSeparated2,
    itemScope: boolean2,
    itemType: spaceSeparated2,
    kind: null,
    label: null,
    lang: null,
    language: null,
    list: null,
    loading: null,
    loop: boolean2,
    low: number2,
    manifest: null,
    max: null,
    maxLength: number2,
    media: null,
    method: null,
    min: null,
    minLength: number2,
    multiple: boolean2,
    muted: boolean2,
    name: null,
    nonce: null,
    noModule: boolean2,
    noValidate: boolean2,
    onAbort: null,
    onAfterPrint: null,
    onAuxClick: null,
    onBeforeMatch: null,
    onBeforePrint: null,
    onBeforeToggle: null,
    onBeforeUnload: null,
    onBlur: null,
    onCancel: null,
    onCanPlay: null,
    onCanPlayThrough: null,
    onChange: null,
    onClick: null,
    onClose: null,
    onContextLost: null,
    onContextMenu: null,
    onContextRestored: null,
    onCopy: null,
    onCueChange: null,
    onCut: null,
    onDblClick: null,
    onDrag: null,
    onDragEnd: null,
    onDragEnter: null,
    onDragExit: null,
    onDragLeave: null,
    onDragOver: null,
    onDragStart: null,
    onDrop: null,
    onDurationChange: null,
    onEmptied: null,
    onEnded: null,
    onError: null,
    onFocus: null,
    onFormData: null,
    onHashChange: null,
    onInput: null,
    onInvalid: null,
    onKeyDown: null,
    onKeyPress: null,
    onKeyUp: null,
    onLanguageChange: null,
    onLoad: null,
    onLoadedData: null,
    onLoadedMetadata: null,
    onLoadEnd: null,
    onLoadStart: null,
    onMessage: null,
    onMessageError: null,
    onMouseDown: null,
    onMouseEnter: null,
    onMouseLeave: null,
    onMouseMove: null,
    onMouseOut: null,
    onMouseOver: null,
    onMouseUp: null,
    onOffline: null,
    onOnline: null,
    onPageHide: null,
    onPageShow: null,
    onPaste: null,
    onPause: null,
    onPlay: null,
    onPlaying: null,
    onPopState: null,
    onProgress: null,
    onRateChange: null,
    onRejectionHandled: null,
    onReset: null,
    onResize: null,
    onScroll: null,
    onScrollEnd: null,
    onSecurityPolicyViolation: null,
    onSeeked: null,
    onSeeking: null,
    onSelect: null,
    onSlotChange: null,
    onStalled: null,
    onStorage: null,
    onSubmit: null,
    onSuspend: null,
    onTimeUpdate: null,
    onToggle: null,
    onUnhandledRejection: null,
    onUnload: null,
    onVolumeChange: null,
    onWaiting: null,
    onWheel: null,
    open: boolean2,
    optimum: number2,
    pattern: null,
    ping: spaceSeparated2,
    placeholder: null,
    playsInline: boolean2,
    popover: null,
    popoverTarget: null,
    popoverTargetAction: null,
    poster: null,
    preload: null,
    readOnly: boolean2,
    referrerPolicy: null,
    rel: spaceSeparated2,
    required: boolean2,
    reversed: boolean2,
    rows: number2,
    rowSpan: number2,
    sandbox: spaceSeparated2,
    scope: null,
    scoped: boolean2,
    seamless: boolean2,
    selected: boolean2,
    shadowRootClonable: boolean2,
    shadowRootDelegatesFocus: boolean2,
    shadowRootMode: null,
    shape: null,
    size: number2,
    sizes: null,
    slot: null,
    span: number2,
    spellCheck: booleanish2,
    src: null,
    srcDoc: null,
    srcLang: null,
    srcSet: null,
    start: number2,
    step: null,
    style: null,
    tabIndex: number2,
    target: null,
    title: null,
    translate: null,
    type: null,
    typeMustMatch: boolean2,
    useMap: null,
    value: booleanish2,
    width: number2,
    wrap: null,
    writingSuggestions: null,
    // Legacy.
    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis
    align: null,
    // Several. Use CSS `text-align` instead,
    aLink: null,
    // `<body>`. Use CSS `a:active {color}` instead
    archive: spaceSeparated2,
    // `<object>`. List of URIs to archives
    axis: null,
    // `<td>` and `<th>`. Use `scope` on `<th>`
    background: null,
    // `<body>`. Use CSS `background-image` instead
    bgColor: null,
    // `<body>` and table elements. Use CSS `background-color` instead
    border: number2,
    // `<table>`. Use CSS `border-width` instead,
    borderColor: null,
    // `<table>`. Use CSS `border-color` instead,
    bottomMargin: number2,
    // `<body>`
    cellPadding: null,
    // `<table>`
    cellSpacing: null,
    // `<table>`
    char: null,
    // Several table elements. When `align=char`, sets the character to align on
    charOff: null,
    // Several table elements. When `char`, offsets the alignment
    classId: null,
    // `<object>`
    clear: null,
    // `<br>`. Use CSS `clear` instead
    code: null,
    // `<object>`
    codeBase: null,
    // `<object>`
    codeType: null,
    // `<object>`
    color: null,
    // `<font>` and `<hr>`. Use CSS instead
    compact: boolean2,
    // Lists. Use CSS to reduce space between items instead
    declare: boolean2,
    // `<object>`
    event: null,
    // `<script>`
    face: null,
    // `<font>`. Use CSS instead
    frame: null,
    // `<table>`
    frameBorder: null,
    // `<iframe>`. Use CSS `border` instead
    hSpace: number2,
    // `<img>` and `<object>`
    leftMargin: number2,
    // `<body>`
    link: null,
    // `<body>`. Use CSS `a:link {color: *}` instead
    longDesc: null,
    // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`
    lowSrc: null,
    // `<img>`. Use a `<picture>`
    marginHeight: number2,
    // `<body>`
    marginWidth: number2,
    // `<body>`
    noResize: boolean2,
    // `<frame>`
    noHref: boolean2,
    // `<area>`. Use no href instead of an explicit `nohref`
    noShade: boolean2,
    // `<hr>`. Use background-color and height instead of borders
    noWrap: boolean2,
    // `<td>` and `<th>`
    object: null,
    // `<applet>`
    profile: null,
    // `<head>`
    prompt: null,
    // `<isindex>`
    rev: null,
    // `<link>`
    rightMargin: number2,
    // `<body>`
    rules: null,
    // `<table>`
    scheme: null,
    // `<meta>`
    scrolling: booleanish2,
    // `<frame>`. Use overflow in the child context
    standby: null,
    // `<object>`
    summary: null,
    // `<table>`
    text: null,
    // `<body>`. Use CSS `color` instead
    topMargin: number2,
    // `<body>`
    valueType: null,
    // `<param>`
    version: null,
    // `<html>`. Use a doctype.
    vAlign: null,
    // Several. Use CSS `vertical-align` instead
    vLink: null,
    // `<body>`. Use CSS `a:visited {color}` instead
    vSpace: number2,
    // `<img>` and `<object>`
    // Non-standard Properties.
    allowTransparency: null,
    autoCorrect: null,
    autoSave: null,
    disablePictureInPicture: boolean2,
    disableRemotePlayback: boolean2,
    prefix: null,
    property: null,
    results: number2,
    security: null,
    unselectable: null
  }
});

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/svg.js
var svg3 = create2({
  space: "svg",
  attributes: {
    accentHeight: "accent-height",
    alignmentBaseline: "alignment-baseline",
    arabicForm: "arabic-form",
    baselineShift: "baseline-shift",
    capHeight: "cap-height",
    className: "class",
    clipPath: "clip-path",
    clipRule: "clip-rule",
    colorInterpolation: "color-interpolation",
    colorInterpolationFilters: "color-interpolation-filters",
    colorProfile: "color-profile",
    colorRendering: "color-rendering",
    crossOrigin: "crossorigin",
    dataType: "datatype",
    dominantBaseline: "dominant-baseline",
    enableBackground: "enable-background",
    fillOpacity: "fill-opacity",
    fillRule: "fill-rule",
    floodColor: "flood-color",
    floodOpacity: "flood-opacity",
    fontFamily: "font-family",
    fontSize: "font-size",
    fontSizeAdjust: "font-size-adjust",
    fontStretch: "font-stretch",
    fontStyle: "font-style",
    fontVariant: "font-variant",
    fontWeight: "font-weight",
    glyphName: "glyph-name",
    glyphOrientationHorizontal: "glyph-orientation-horizontal",
    glyphOrientationVertical: "glyph-orientation-vertical",
    hrefLang: "hreflang",
    horizAdvX: "horiz-adv-x",
    horizOriginX: "horiz-origin-x",
    horizOriginY: "horiz-origin-y",
    imageRendering: "image-rendering",
    letterSpacing: "letter-spacing",
    lightingColor: "lighting-color",
    markerEnd: "marker-end",
    markerMid: "marker-mid",
    markerStart: "marker-start",
    navDown: "nav-down",
    navDownLeft: "nav-down-left",
    navDownRight: "nav-down-right",
    navLeft: "nav-left",
    navNext: "nav-next",
    navPrev: "nav-prev",
    navRight: "nav-right",
    navUp: "nav-up",
    navUpLeft: "nav-up-left",
    navUpRight: "nav-up-right",
    onAbort: "onabort",
    onActivate: "onactivate",
    onAfterPrint: "onafterprint",
    onBeforePrint: "onbeforeprint",
    onBegin: "onbegin",
    onCancel: "oncancel",
    onCanPlay: "oncanplay",
    onCanPlayThrough: "oncanplaythrough",
    onChange: "onchange",
    onClick: "onclick",
    onClose: "onclose",
    onCopy: "oncopy",
    onCueChange: "oncuechange",
    onCut: "oncut",
    onDblClick: "ondblclick",
    onDrag: "ondrag",
    onDragEnd: "ondragend",
    onDragEnter: "ondragenter",
    onDragExit: "ondragexit",
    onDragLeave: "ondragleave",
    onDragOver: "ondragover",
    onDragStart: "ondragstart",
    onDrop: "ondrop",
    onDurationChange: "ondurationchange",
    onEmptied: "onemptied",
    onEnd: "onend",
    onEnded: "onended",
    onError: "onerror",
    onFocus: "onfocus",
    onFocusIn: "onfocusin",
    onFocusOut: "onfocusout",
    onHashChange: "onhashchange",
    onInput: "oninput",
    onInvalid: "oninvalid",
    onKeyDown: "onkeydown",
    onKeyPress: "onkeypress",
    onKeyUp: "onkeyup",
    onLoad: "onload",
    onLoadedData: "onloadeddata",
    onLoadedMetadata: "onloadedmetadata",
    onLoadStart: "onloadstart",
    onMessage: "onmessage",
    onMouseDown: "onmousedown",
    onMouseEnter: "onmouseenter",
    onMouseLeave: "onmouseleave",
    onMouseMove: "onmousemove",
    onMouseOut: "onmouseout",
    onMouseOver: "onmouseover",
    onMouseUp: "onmouseup",
    onMouseWheel: "onmousewheel",
    onOffline: "onoffline",
    onOnline: "ononline",
    onPageHide: "onpagehide",
    onPageShow: "onpageshow",
    onPaste: "onpaste",
    onPause: "onpause",
    onPlay: "onplay",
    onPlaying: "onplaying",
    onPopState: "onpopstate",
    onProgress: "onprogress",
    onRateChange: "onratechange",
    onRepeat: "onrepeat",
    onReset: "onreset",
    onResize: "onresize",
    onScroll: "onscroll",
    onSeeked: "onseeked",
    onSeeking: "onseeking",
    onSelect: "onselect",
    onShow: "onshow",
    onStalled: "onstalled",
    onStorage: "onstorage",
    onSubmit: "onsubmit",
    onSuspend: "onsuspend",
    onTimeUpdate: "ontimeupdate",
    onToggle: "ontoggle",
    onUnload: "onunload",
    onVolumeChange: "onvolumechange",
    onWaiting: "onwaiting",
    onZoom: "onzoom",
    overlinePosition: "overline-position",
    overlineThickness: "overline-thickness",
    paintOrder: "paint-order",
    panose1: "panose-1",
    pointerEvents: "pointer-events",
    referrerPolicy: "referrerpolicy",
    renderingIntent: "rendering-intent",
    shapeRendering: "shape-rendering",
    stopColor: "stop-color",
    stopOpacity: "stop-opacity",
    strikethroughPosition: "strikethrough-position",
    strikethroughThickness: "strikethrough-thickness",
    strokeDashArray: "stroke-dasharray",
    strokeDashOffset: "stroke-dashoffset",
    strokeLineCap: "stroke-linecap",
    strokeLineJoin: "stroke-linejoin",
    strokeMiterLimit: "stroke-miterlimit",
    strokeOpacity: "stroke-opacity",
    strokeWidth: "stroke-width",
    tabIndex: "tabindex",
    textAnchor: "text-anchor",
    textDecoration: "text-decoration",
    textRendering: "text-rendering",
    transformOrigin: "transform-origin",
    typeOf: "typeof",
    underlinePosition: "underline-position",
    underlineThickness: "underline-thickness",
    unicodeBidi: "unicode-bidi",
    unicodeRange: "unicode-range",
    unitsPerEm: "units-per-em",
    vAlphabetic: "v-alphabetic",
    vHanging: "v-hanging",
    vIdeographic: "v-ideographic",
    vMathematical: "v-mathematical",
    vectorEffect: "vector-effect",
    vertAdvY: "vert-adv-y",
    vertOriginX: "vert-origin-x",
    vertOriginY: "vert-origin-y",
    wordSpacing: "word-spacing",
    writingMode: "writing-mode",
    xHeight: "x-height",
    // These were camelcased in Tiny. Now lowercased in SVG 2
    playbackOrder: "playbackorder",
    timelineBegin: "timelinebegin"
  },
  transform: caseSensitiveTransform2,
  properties: {
    about: commaOrSpaceSeparated2,
    accentHeight: number2,
    accumulate: null,
    additive: null,
    alignmentBaseline: null,
    alphabetic: number2,
    amplitude: number2,
    arabicForm: null,
    ascent: number2,
    attributeName: null,
    attributeType: null,
    azimuth: number2,
    bandwidth: null,
    baselineShift: null,
    baseFrequency: null,
    baseProfile: null,
    bbox: null,
    begin: null,
    bias: number2,
    by: null,
    calcMode: null,
    capHeight: number2,
    className: spaceSeparated2,
    clip: null,
    clipPath: null,
    clipPathUnits: null,
    clipRule: null,
    color: null,
    colorInterpolation: null,
    colorInterpolationFilters: null,
    colorProfile: null,
    colorRendering: null,
    content: null,
    contentScriptType: null,
    contentStyleType: null,
    crossOrigin: null,
    cursor: null,
    cx: null,
    cy: null,
    d: null,
    dataType: null,
    defaultAction: null,
    descent: number2,
    diffuseConstant: number2,
    direction: null,
    display: null,
    dur: null,
    divisor: number2,
    dominantBaseline: null,
    download: boolean2,
    dx: null,
    dy: null,
    edgeMode: null,
    editable: null,
    elevation: number2,
    enableBackground: null,
    end: null,
    event: null,
    exponent: number2,
    externalResourcesRequired: null,
    fill: null,
    fillOpacity: number2,
    fillRule: null,
    filter: null,
    filterRes: null,
    filterUnits: null,
    floodColor: null,
    floodOpacity: null,
    focusable: null,
    focusHighlight: null,
    fontFamily: null,
    fontSize: null,
    fontSizeAdjust: null,
    fontStretch: null,
    fontStyle: null,
    fontVariant: null,
    fontWeight: null,
    format: null,
    fr: null,
    from: null,
    fx: null,
    fy: null,
    g1: commaSeparated2,
    g2: commaSeparated2,
    glyphName: commaSeparated2,
    glyphOrientationHorizontal: null,
    glyphOrientationVertical: null,
    glyphRef: null,
    gradientTransform: null,
    gradientUnits: null,
    handler: null,
    hanging: number2,
    hatchContentUnits: null,
    hatchUnits: null,
    height: null,
    href: null,
    hrefLang: null,
    horizAdvX: number2,
    horizOriginX: number2,
    horizOriginY: number2,
    id: null,
    ideographic: number2,
    imageRendering: null,
    initialVisibility: null,
    in: null,
    in2: null,
    intercept: number2,
    k: number2,
    k1: number2,
    k2: number2,
    k3: number2,
    k4: number2,
    kernelMatrix: commaOrSpaceSeparated2,
    kernelUnitLength: null,
    keyPoints: null,
    // SEMI_COLON_SEPARATED
    keySplines: null,
    // SEMI_COLON_SEPARATED
    keyTimes: null,
    // SEMI_COLON_SEPARATED
    kerning: null,
    lang: null,
    lengthAdjust: null,
    letterSpacing: null,
    lightingColor: null,
    limitingConeAngle: number2,
    local: null,
    markerEnd: null,
    markerMid: null,
    markerStart: null,
    markerHeight: null,
    markerUnits: null,
    markerWidth: null,
    mask: null,
    maskContentUnits: null,
    maskUnits: null,
    mathematical: null,
    max: null,
    media: null,
    mediaCharacterEncoding: null,
    mediaContentEncodings: null,
    mediaSize: number2,
    mediaTime: null,
    method: null,
    min: null,
    mode: null,
    name: null,
    navDown: null,
    navDownLeft: null,
    navDownRight: null,
    navLeft: null,
    navNext: null,
    navPrev: null,
    navRight: null,
    navUp: null,
    navUpLeft: null,
    navUpRight: null,
    numOctaves: null,
    observer: null,
    offset: null,
    onAbort: null,
    onActivate: null,
    onAfterPrint: null,
    onBeforePrint: null,
    onBegin: null,
    onCancel: null,
    onCanPlay: null,
    onCanPlayThrough: null,
    onChange: null,
    onClick: null,
    onClose: null,
    onCopy: null,
    onCueChange: null,
    onCut: null,
    onDblClick: null,
    onDrag: null,
    onDragEnd: null,
    onDragEnter: null,
    onDragExit: null,
    onDragLeave: null,
    onDragOver: null,
    onDragStart: null,
    onDrop: null,
    onDurationChange: null,
    onEmptied: null,
    onEnd: null,
    onEnded: null,
    onError: null,
    onFocus: null,
    onFocusIn: null,
    onFocusOut: null,
    onHashChange: null,
    onInput: null,
    onInvalid: null,
    onKeyDown: null,
    onKeyPress: null,
    onKeyUp: null,
    onLoad: null,
    onLoadedData: null,
    onLoadedMetadata: null,
    onLoadStart: null,
    onMessage: null,
    onMouseDown: null,
    onMouseEnter: null,
    onMouseLeave: null,
    onMouseMove: null,
    onMouseOut: null,
    onMouseOver: null,
    onMouseUp: null,
    onMouseWheel: null,
    onOffline: null,
    onOnline: null,
    onPageHide: null,
    onPageShow: null,
    onPaste: null,
    onPause: null,
    onPlay: null,
    onPlaying: null,
    onPopState: null,
    onProgress: null,
    onRateChange: null,
    onRepeat: null,
    onReset: null,
    onResize: null,
    onScroll: null,
    onSeeked: null,
    onSeeking: null,
    onSelect: null,
    onShow: null,
    onStalled: null,
    onStorage: null,
    onSubmit: null,
    onSuspend: null,
    onTimeUpdate: null,
    onToggle: null,
    onUnload: null,
    onVolumeChange: null,
    onWaiting: null,
    onZoom: null,
    opacity: null,
    operator: null,
    order: null,
    orient: null,
    orientation: null,
    origin: null,
    overflow: null,
    overlay: null,
    overlinePosition: number2,
    overlineThickness: number2,
    paintOrder: null,
    panose1: null,
    path: null,
    pathLength: number2,
    patternContentUnits: null,
    patternTransform: null,
    patternUnits: null,
    phase: null,
    ping: spaceSeparated2,
    pitch: null,
    playbackOrder: null,
    pointerEvents: null,
    points: null,
    pointsAtX: number2,
    pointsAtY: number2,
    pointsAtZ: number2,
    preserveAlpha: null,
    preserveAspectRatio: null,
    primitiveUnits: null,
    propagate: null,
    property: commaOrSpaceSeparated2,
    r: null,
    radius: null,
    referrerPolicy: null,
    refX: null,
    refY: null,
    rel: commaOrSpaceSeparated2,
    rev: commaOrSpaceSeparated2,
    renderingIntent: null,
    repeatCount: null,
    repeatDur: null,
    requiredExtensions: commaOrSpaceSeparated2,
    requiredFeatures: commaOrSpaceSeparated2,
    requiredFonts: commaOrSpaceSeparated2,
    requiredFormats: commaOrSpaceSeparated2,
    resource: null,
    restart: null,
    result: null,
    rotate: null,
    rx: null,
    ry: null,
    scale: null,
    seed: null,
    shapeRendering: null,
    side: null,
    slope: null,
    snapshotTime: null,
    specularConstant: number2,
    specularExponent: number2,
    spreadMethod: null,
    spacing: null,
    startOffset: null,
    stdDeviation: null,
    stemh: null,
    stemv: null,
    stitchTiles: null,
    stopColor: null,
    stopOpacity: null,
    strikethroughPosition: number2,
    strikethroughThickness: number2,
    string: null,
    stroke: null,
    strokeDashArray: commaOrSpaceSeparated2,
    strokeDashOffset: null,
    strokeLineCap: null,
    strokeLineJoin: null,
    strokeMiterLimit: number2,
    strokeOpacity: number2,
    strokeWidth: null,
    style: null,
    surfaceScale: number2,
    syncBehavior: null,
    syncBehaviorDefault: null,
    syncMaster: null,
    syncTolerance: null,
    syncToleranceDefault: null,
    systemLanguage: commaOrSpaceSeparated2,
    tabIndex: number2,
    tableValues: null,
    target: null,
    targetX: number2,
    targetY: number2,
    textAnchor: null,
    textDecoration: null,
    textRendering: null,
    textLength: null,
    timelineBegin: null,
    title: null,
    transformBehavior: null,
    type: null,
    typeOf: commaOrSpaceSeparated2,
    to: null,
    transform: null,
    transformOrigin: null,
    u1: null,
    u2: null,
    underlinePosition: number2,
    underlineThickness: number2,
    unicode: null,
    unicodeBidi: null,
    unicodeRange: null,
    unitsPerEm: number2,
    values: null,
    vAlphabetic: number2,
    vMathematical: number2,
    vectorEffect: null,
    vHanging: number2,
    vIdeographic: number2,
    version: null,
    vertAdvY: number2,
    vertOriginX: number2,
    vertOriginY: number2,
    viewBox: null,
    viewTarget: null,
    visibility: null,
    width: null,
    widths: null,
    wordSpacing: null,
    writingMode: null,
    x: null,
    x1: null,
    x2: null,
    xChannelSelector: null,
    xHeight: number2,
    y: null,
    y1: null,
    y2: null,
    yChannelSelector: null,
    z: null,
    zoomAndPan: null
  }
});

// node_modules/hast-util-to-parse5/node_modules/property-information/lib/find.js
var valid2 = /^data[-\w.:]+$/i;
var dash2 = /-[a-z]/g;
var cap2 = /[A-Z]/g;
function find2(schema, value) {
  const normal = normalize2(value);
  let prop = value;
  let Type = Info2;
  if (normal in schema.normal) {
    return schema.property[schema.normal[normal]];
  }
  if (normal.length > 4 && normal.slice(0, 4) === "data" && valid2.test(value)) {
    if (value.charAt(4) === "-") {
      const rest = value.slice(5).replace(dash2, camelcase2);
      prop = "data" + rest.charAt(0).toUpperCase() + rest.slice(1);
    } else {
      const rest = value.slice(4);
      if (!dash2.test(rest)) {
        let dashes = rest.replace(cap2, kebab2);
        if (dashes.charAt(0) !== "-") {
          dashes = "-" + dashes;
        }
        value = "data" + dashes;
      }
    }
    Type = DefinedInfo2;
  }
  return new Type(prop, value);
}
function kebab2($0) {
  return "-" + $0.toLowerCase();
}
function camelcase2($0) {
  return $0.charAt(1).toUpperCase();
}

// node_modules/hast-util-to-parse5/node_modules/property-information/index.js
var html4 = merge2([xml2, xlink2, xmlns2, aria2, html3], "html");
var svg4 = merge2([xml2, xlink2, xmlns2, aria2, svg3], "svg");

// node_modules/hast-util-to-parse5/lib/index.js
var emptyOptions = {};
var own3 = {}.hasOwnProperty;
var one2 = zwitch("type", { handlers: { root, element: element2, text, comment, doctype } });
function toParse5(tree, options) {
  const settings = options || emptyOptions;
  const space = settings.space;
  return one2(tree, space === "svg" ? svg4 : html4);
}
function root(node, schema) {
  const result = {
    nodeName: "#document",
    // @ts-expect-error: `parse5` uses enums, which are actually strings.
    mode: (node.data || {}).quirksMode ? "quirks" : "no-quirks",
    childNodes: []
  };
  result.childNodes = all2(node.children, result, schema);
  patch2(node, result);
  return result;
}
function fragment(node, schema) {
  const result = { nodeName: "#document-fragment", childNodes: [] };
  result.childNodes = all2(node.children, result, schema);
  patch2(node, result);
  return result;
}
function doctype(node) {
  const result = {
    nodeName: "#documentType",
    name: "html",
    publicId: "",
    systemId: "",
    parentNode: null
  };
  patch2(node, result);
  return result;
}
function text(node) {
  const result = {
    nodeName: "#text",
    value: node.value,
    parentNode: null
  };
  patch2(node, result);
  return result;
}
function comment(node) {
  const result = {
    nodeName: "#comment",
    data: node.value,
    parentNode: null
  };
  patch2(node, result);
  return result;
}
function element2(node, schema) {
  const parentSchema = schema;
  let currentSchema = parentSchema;
  if (node.type === "element" && node.tagName.toLowerCase() === "svg" && parentSchema.space === "html") {
    currentSchema = svg4;
  }
  const attrs = [];
  let prop;
  if (node.properties) {
    for (prop in node.properties) {
      if (prop !== "children" && own3.call(node.properties, prop)) {
        const result2 = createProperty(
          currentSchema,
          prop,
          node.properties[prop]
        );
        if (result2) {
          attrs.push(result2);
        }
      }
    }
  }
  const space = currentSchema.space;
  ok(space);
  const result = {
    nodeName: node.tagName,
    tagName: node.tagName,
    attrs,
    // @ts-expect-error: `parse5` types are wrong.
    namespaceURI: webNamespaces[space],
    childNodes: [],
    parentNode: null
  };
  result.childNodes = all2(node.children, result, currentSchema);
  patch2(node, result);
  if (node.tagName === "template" && node.content) {
    result.content = fragment(node.content, currentSchema);
  }
  return result;
}
function createProperty(schema, prop, value) {
  const info = find2(schema, prop);
  if (value === false || value === null || value === void 0 || typeof value === "number" && Number.isNaN(value) || !value && info.boolean) {
    return;
  }
  if (Array.isArray(value)) {
    value = info.commaSeparated ? stringify(value) : stringify2(value);
  }
  const attribute = {
    name: info.attribute,
    value: value === true ? "" : String(value)
  };
  if (info.space && info.space !== "html" && info.space !== "svg") {
    const index = attribute.name.indexOf(":");
    if (index < 0) {
      attribute.prefix = "";
    } else {
      attribute.name = attribute.name.slice(index + 1);
      attribute.prefix = info.attribute.slice(0, index);
    }
    attribute.namespace = webNamespaces[info.space];
  }
  return attribute;
}
function all2(children, parentNode, schema) {
  let index = -1;
  const results = [];
  if (children) {
    while (++index < children.length) {
      const child = one2(children[index], schema);
      child.parentNode = parentNode;
      results.push(child);
    }
  }
  return results;
}
function patch2(from, to) {
  const position2 = from.position;
  if (position2 && position2.start && position2.end) {
    ok(typeof position2.start.offset === "number");
    ok(typeof position2.end.offset === "number");
    to.sourceCodeLocation = {
      startLine: position2.start.line,
      startCol: position2.start.column,
      startOffset: position2.start.offset,
      endLine: position2.end.line,
      endCol: position2.end.column,
      endOffset: position2.end.offset
    };
  }
}

// node_modules/html-void-elements/index.js
var htmlVoidElements = [
  "area",
  "base",
  "basefont",
  "bgsound",
  "br",
  "col",
  "command",
  "embed",
  "frame",
  "hr",
  "image",
  "img",
  "input",
  "keygen",
  "link",
  "meta",
  "param",
  "source",
  "track",
  "wbr"
];

// node_modules/hast-util-raw/lib/index.js
var gfmTagfilterExpression = /<(\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\t\n\f\r />])/gi;
var knownMdxNames = /* @__PURE__ */ new Set([
  "mdxFlowExpression",
  "mdxJsxFlowElement",
  "mdxJsxTextElement",
  "mdxTextExpression",
  "mdxjsEsm"
]);
var parseOptions = { sourceCodeLocationInfo: true, scriptingEnabled: false };
function raw(tree, options) {
  const document = documentMode(tree);
  const one3 = zwitch("type", {
    handlers: { root: root2, element: element3, text: text2, comment: comment2, doctype: doctype2, raw: handleRaw },
    unknown
  });
  const state = {
    parser: document ? new Parser(parseOptions) : Parser.getFragmentParser(void 0, parseOptions),
    handle(node) {
      one3(node, state);
    },
    stitches: false,
    options: options || {}
  };
  one3(tree, state);
  resetTokenizer(state, pointStart());
  const p5 = document ? state.parser.document : state.parser.getFragment();
  const result = fromParse5(p5, {
    // To do: support `space`?
    file: state.options.file
  });
  if (state.stitches) {
    visit(result, "comment", function(node, index, parent) {
      const stitch2 = (
        /** @type {Stitch} */
        /** @type {unknown} */
        node
      );
      if (stitch2.value.stitch && parent && index !== void 0) {
        const siblings = parent.children;
        siblings[index] = stitch2.value.stitch;
        return index;
      }
    });
  }
  if (result.type === "root" && result.children.length === 1 && result.children[0].type === tree.type) {
    return result.children[0];
  }
  return result;
}
function all3(nodes, state) {
  let index = -1;
  if (nodes) {
    while (++index < nodes.length) {
      state.handle(nodes[index]);
    }
  }
}
function root2(node, state) {
  all3(node.children, state);
}
function element3(node, state) {
  startTag(node, state);
  all3(node.children, state);
  endTag(node, state);
}
function text2(node, state) {
  if (state.parser.tokenizer.state > 4) {
    state.parser.tokenizer.state = 0;
  }
  const token = {
    type: token_exports.TokenType.CHARACTER,
    chars: node.value,
    location: createParse5Location(node)
  };
  resetTokenizer(state, pointStart(node));
  state.parser.currentToken = token;
  state.parser._processToken(state.parser.currentToken);
}
function doctype2(node, state) {
  const token = {
    type: token_exports.TokenType.DOCTYPE,
    name: "html",
    forceQuirks: false,
    publicId: "",
    systemId: "",
    location: createParse5Location(node)
  };
  resetTokenizer(state, pointStart(node));
  state.parser.currentToken = token;
  state.parser._processToken(state.parser.currentToken);
}
function stitch(node, state) {
  state.stitches = true;
  const clone = cloneWithoutChildren(node);
  if ("children" in node && "children" in clone) {
    const fakeRoot = (
      /** @type {Root} */
      raw({ type: "root", children: node.children }, state.options)
    );
    clone.children = fakeRoot.children;
  }
  comment2({ type: "comment", value: { stitch: clone } }, state);
}
function comment2(node, state) {
  const data = node.value;
  const token = {
    type: token_exports.TokenType.COMMENT,
    data,
    location: createParse5Location(node)
  };
  resetTokenizer(state, pointStart(node));
  state.parser.currentToken = token;
  state.parser._processToken(state.parser.currentToken);
}
function handleRaw(node, state) {
  state.parser.tokenizer.preprocessor.html = "";
  state.parser.tokenizer.preprocessor.pos = -1;
  state.parser.tokenizer.preprocessor.lastGapPos = -2;
  state.parser.tokenizer.preprocessor.gapStack = [];
  state.parser.tokenizer.preprocessor.skipNextNewLine = false;
  state.parser.tokenizer.preprocessor.lastChunkWritten = false;
  state.parser.tokenizer.preprocessor.endOfChunkHit = false;
  state.parser.tokenizer.preprocessor.isEol = false;
  setPoint(state, pointStart(node));
  state.parser.tokenizer.write(
    state.options.tagfilter ? node.value.replace(gfmTagfilterExpression, "&lt;$1$2") : node.value,
    false
  );
  state.parser.tokenizer._runParsingLoop();
  if (state.parser.tokenizer.state === 72 || // @ts-expect-error: removed.
  state.parser.tokenizer.state === 78) {
    state.parser.tokenizer.preprocessor.lastChunkWritten = true;
    const cp = state.parser.tokenizer._consume();
    state.parser.tokenizer._callState(cp);
  }
}
function unknown(node_, state) {
  const node = (
    /** @type {Nodes} */
    node_
  );
  if (state.options.passThrough && state.options.passThrough.includes(node.type)) {
    stitch(node, state);
  } else {
    let extra = "";
    if (knownMdxNames.has(node.type)) {
      extra = ". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax";
    }
    throw new Error("Cannot compile `" + node.type + "` node" + extra);
  }
}
function resetTokenizer(state, point2) {
  setPoint(state, point2);
  const token = state.parser.tokenizer.currentCharacterToken;
  if (token && token.location) {
    token.location.endLine = state.parser.tokenizer.preprocessor.line;
    token.location.endCol = state.parser.tokenizer.preprocessor.col + 1;
    token.location.endOffset = state.parser.tokenizer.preprocessor.offset + 1;
    state.parser.currentToken = token;
    state.parser._processToken(state.parser.currentToken);
  }
  state.parser.tokenizer.paused = false;
  state.parser.tokenizer.inLoop = false;
  state.parser.tokenizer.active = false;
  state.parser.tokenizer.returnState = TokenizerMode.DATA;
  state.parser.tokenizer.charRefCode = -1;
  state.parser.tokenizer.consumedAfterSnapshot = -1;
  state.parser.tokenizer.currentLocation = null;
  state.parser.tokenizer.currentCharacterToken = null;
  state.parser.tokenizer.currentToken = null;
  state.parser.tokenizer.currentAttr = { name: "", value: "" };
}
function setPoint(state, point2) {
  if (point2 && point2.offset !== void 0) {
    const location2 = {
      startLine: point2.line,
      startCol: point2.column,
      startOffset: point2.offset,
      endLine: -1,
      endCol: -1,
      endOffset: -1
    };
    state.parser.tokenizer.preprocessor.lineStartPos = -point2.column + 1;
    state.parser.tokenizer.preprocessor.droppedBufferSize = point2.offset;
    state.parser.tokenizer.preprocessor.line = point2.line;
    state.parser.tokenizer.currentLocation = location2;
  }
}
function startTag(node, state) {
  const tagName = node.tagName.toLowerCase();
  if (state.parser.tokenizer.state === TokenizerMode.PLAINTEXT) return;
  resetTokenizer(state, pointStart(node));
  const current = state.parser.openElements.current;
  let ns = "namespaceURI" in current ? current.namespaceURI : webNamespaces.html;
  if (ns === webNamespaces.html && tagName === "svg") {
    ns = webNamespaces.svg;
  }
  const result = toParse5(
    // Shallow clone to not delve into `children`: we only need the attributes.
    { ...node, children: [] },
    { space: ns === webNamespaces.svg ? "svg" : "html" }
  );
  const tag = {
    type: token_exports.TokenType.START_TAG,
    tagName,
    tagID: html_exports.getTagID(tagName),
    // We always send start and end tags.
    selfClosing: false,
    ackSelfClosing: false,
    // Always element.
    /* c8 ignore next */
    attrs: "attrs" in result ? result.attrs : [],
    location: createParse5Location(node)
  };
  state.parser.currentToken = tag;
  state.parser._processToken(state.parser.currentToken);
  state.parser.tokenizer.lastStartTagName = tagName;
}
function endTag(node, state) {
  const tagName = node.tagName.toLowerCase();
  if (!state.parser.tokenizer.inForeignNode && htmlVoidElements.includes(tagName)) {
    return;
  }
  if (state.parser.tokenizer.state === TokenizerMode.PLAINTEXT) return;
  resetTokenizer(state, pointEnd(node));
  const tag = {
    type: token_exports.TokenType.END_TAG,
    tagName,
    tagID: html_exports.getTagID(tagName),
    selfClosing: false,
    ackSelfClosing: false,
    attrs: [],
    location: createParse5Location(node)
  };
  state.parser.currentToken = tag;
  state.parser._processToken(state.parser.currentToken);
  if (
    // Current element is closed.
    tagName === state.parser.tokenizer.lastStartTagName && // `<textarea>` and `<title>`
    (state.parser.tokenizer.state === TokenizerMode.RCDATA || // `<iframe>`, `<noembed>`, `<noframes>`, `<style>`, `<xmp>`
    state.parser.tokenizer.state === TokenizerMode.RAWTEXT || // `<script>`
    state.parser.tokenizer.state === TokenizerMode.SCRIPT_DATA)
  ) {
    state.parser.tokenizer.state = TokenizerMode.DATA;
  }
}
function documentMode(node) {
  const head = node.type === "root" ? node.children[0] : node;
  return Boolean(
    head && (head.type === "doctype" || head.type === "element" && head.tagName.toLowerCase() === "html")
  );
}
function createParse5Location(node) {
  const start = pointStart(node) || {
    line: void 0,
    column: void 0,
    offset: void 0
  };
  const end = pointEnd(node) || {
    line: void 0,
    column: void 0,
    offset: void 0
  };
  const location2 = {
    startLine: start.line,
    startCol: start.column,
    startOffset: start.offset,
    endLine: end.line,
    endCol: end.column,
    endOffset: end.offset
  };
  return location2;
}
function cloneWithoutChildren(node) {
  return "children" in node ? esm_default({ ...node, children: [] }) : esm_default(node);
}

// node_modules/rehype-raw/lib/index.js
function rehypeRaw(options) {
  return function(tree, file) {
    const result = (
      /** @type {Root} */
      raw(tree, { ...options, file })
    );
    return result;
  };
}
export {
  rehypeRaw as default
};
//# sourceMappingURL=@nuxt_content___@nuxtjs_mdc___rehype-raw.js.map
