import {
  assign,
  create,
  createEmitter,
  deepCopy,
  escapeHtml,
  format,
  friendlyJSONstringify,
  generateCodeFrame,
  generateFormatCacheKey,
  getGlobalThis,
  hasOwn,
  inBrowser,
  isArray,
  isBoolean,
  isDate,
  isEmptyObject,
  isFunction,
  isNumber,
  isObject,
  isPlainObject,
  isPromise,
  isRegExp,
  isString,
  isSymbol,
  join,
  makeSymbol,
  mark,
  measure,
  objectToString,
  sanitizeTranslatedHtml,
  toDisplayString,
  toTypeString,
  warn,
  warnOnce
} from "./chunk-YTJR5NNW.js";
export {
  assign,
  create,
  createEmitter,
  deepCopy,
  escapeHtml,
  format,
  friendlyJSONstringify,
  generateCodeFrame,
  generateFormatCacheKey,
  getGlobalThis,
  hasOwn,
  inBrowser,
  isArray,
  isBoolean,
  isDate,
  isEmptyObject,
  isFunction,
  isNumber,
  isObject,
  isPlainObject,
  isPromise,
  isRegExp,
  isString,
  isSymbol,
  join,
  makeSymbol,
  mark,
  measure,
  objectToString,
  sanitizeTranslatedHtml,
  toDisplayString,
  toTypeString,
  warn,
  warnOnce
};
