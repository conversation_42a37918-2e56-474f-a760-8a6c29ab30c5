{"version": 3, "sources": ["../../../../escape-string-regexp/index.js", "../../../../mdast-util-find-and-replace/lib/index.js"], "sourcesContent": ["export default function escapeStringRegexp(string) {\n\tif (typeof string !== 'string') {\n\t\tthrow new TypeError('Expected a string');\n\t}\n\n\t// Escape characters with special meaning either inside or outside character sets.\n\t// Use a simple backslash escape when it’s always valid, and a `\\xnn` escape when the simpler form would be disallowed by Unicode patterns’ stricter grammar.\n\treturn string\n\t\t.replace(/[|\\\\{}()[\\]^$+*?.]/g, '\\\\$&')\n\t\t.replace(/-/g, '\\\\x2d');\n}\n", "/**\n * @import {<PERSON><PERSON>, <PERSON>, PhrasingContent, <PERSON>, Text} from 'mdast'\n * @import {BuildVisitor, Test, VisitorResult} from 'unist-util-visit-parents'\n */\n\n/**\n * @typedef RegExpMatchObject\n *   Info on the match.\n * @property {number} index\n *   The index of the search at which the result was found.\n * @property {string} input\n *   A copy of the search string in the text node.\n * @property {[...Array<Parents>, Text]} stack\n *   All ancestors of the text node, where the last node is the text itself.\n *\n * @typedef {RegExp | string} Find\n *   Pattern to find.\n *\n *   Strings are escaped and then turned into global expressions.\n *\n * @typedef {Array<FindAndReplaceTuple>} FindAndReplaceList\n *   Several find and replaces, in array form.\n *\n * @typedef {[Find, Replace?]} FindAndReplaceTuple\n *   Find and replace in tuple form.\n *\n * @typedef {ReplaceFunction | string | null | undefined} Replace\n *   Thing to replace with.\n *\n * @callback ReplaceFunction\n *   Callback called when a search matches.\n * @param {...any} parameters\n *   The parameters are the result of corresponding search expression:\n *\n *   * `value` (`string`) — whole match\n *   * `...capture` (`Array<string>`) — matches from regex capture groups\n *   * `match` (`RegExpMatchObject`) — info on the match\n * @returns {Array<PhrasingContent> | PhrasingContent | string | false | null | undefined}\n *   Thing to replace with.\n *\n *   * when `null`, `undefined`, `''`, remove the match\n *   * …or when `false`, do not replace at all\n *   * …or when `string`, replace with a text node of that value\n *   * …or when `Node` or `Array<Node>`, replace with those nodes\n *\n * @typedef {[RegExp, ReplaceFunction]} Pair\n *   Normalized find and replace.\n *\n * @typedef {Array<Pair>} Pairs\n *   All find and replaced.\n *\n * @typedef Options\n *   Configuration.\n * @property {Test | null | undefined} [ignore]\n *   Test for which nodes to ignore (optional).\n */\n\nimport escape from 'escape-string-regexp'\nimport {visitParents} from 'unist-util-visit-parents'\nimport {convert} from 'unist-util-is'\n\n/**\n * Find patterns in a tree and replace them.\n *\n * The algorithm searches the tree in *preorder* for complete values in `Text`\n * nodes.\n * Partial matches are not supported.\n *\n * @param {Nodes} tree\n *   Tree to change.\n * @param {FindAndReplaceList | FindAndReplaceTuple} list\n *   Patterns to find.\n * @param {Options | null | undefined} [options]\n *   Configuration (when `find` is not `Find`).\n * @returns {undefined}\n *   Nothing.\n */\nexport function findAndReplace(tree, list, options) {\n  const settings = options || {}\n  const ignored = convert(settings.ignore || [])\n  const pairs = toPairs(list)\n  let pairIndex = -1\n\n  while (++pairIndex < pairs.length) {\n    visitParents(tree, 'text', visitor)\n  }\n\n  /** @type {BuildVisitor<Root, 'text'>} */\n  function visitor(node, parents) {\n    let index = -1\n    /** @type {Parents | undefined} */\n    let grandparent\n\n    while (++index < parents.length) {\n      const parent = parents[index]\n      /** @type {Array<Nodes> | undefined} */\n      const siblings = grandparent ? grandparent.children : undefined\n\n      if (\n        ignored(\n          parent,\n          siblings ? siblings.indexOf(parent) : undefined,\n          grandparent\n        )\n      ) {\n        return\n      }\n\n      grandparent = parent\n    }\n\n    if (grandparent) {\n      return handler(node, parents)\n    }\n  }\n\n  /**\n   * Handle a text node which is not in an ignored parent.\n   *\n   * @param {Text} node\n   *   Text node.\n   * @param {Array<Parents>} parents\n   *   Parents.\n   * @returns {VisitorResult}\n   *   Result.\n   */\n  function handler(node, parents) {\n    const parent = parents[parents.length - 1]\n    const find = pairs[pairIndex][0]\n    const replace = pairs[pairIndex][1]\n    let start = 0\n    /** @type {Array<Nodes>} */\n    const siblings = parent.children\n    const index = siblings.indexOf(node)\n    let change = false\n    /** @type {Array<PhrasingContent>} */\n    let nodes = []\n\n    find.lastIndex = 0\n\n    let match = find.exec(node.value)\n\n    while (match) {\n      const position = match.index\n      /** @type {RegExpMatchObject} */\n      const matchObject = {\n        index: match.index,\n        input: match.input,\n        stack: [...parents, node]\n      }\n      let value = replace(...match, matchObject)\n\n      if (typeof value === 'string') {\n        value = value.length > 0 ? {type: 'text', value} : undefined\n      }\n\n      // It wasn’t a match after all.\n      if (value === false) {\n        // False acts as if there was no match.\n        // So we need to reset `lastIndex`, which currently being at the end of\n        // the current match, to the beginning.\n        find.lastIndex = position + 1\n      } else {\n        if (start !== position) {\n          nodes.push({\n            type: 'text',\n            value: node.value.slice(start, position)\n          })\n        }\n\n        if (Array.isArray(value)) {\n          nodes.push(...value)\n        } else if (value) {\n          nodes.push(value)\n        }\n\n        start = position + match[0].length\n        change = true\n      }\n\n      if (!find.global) {\n        break\n      }\n\n      match = find.exec(node.value)\n    }\n\n    if (change) {\n      if (start < node.value.length) {\n        nodes.push({type: 'text', value: node.value.slice(start)})\n      }\n\n      parent.children.splice(index, 1, ...nodes)\n    } else {\n      nodes = [node]\n    }\n\n    return index + nodes.length\n  }\n}\n\n/**\n * Turn a tuple or a list of tuples into pairs.\n *\n * @param {FindAndReplaceList | FindAndReplaceTuple} tupleOrList\n *   Schema.\n * @returns {Pairs}\n *   Clean pairs.\n */\nfunction toPairs(tupleOrList) {\n  /** @type {Pairs} */\n  const result = []\n\n  if (!Array.isArray(tupleOrList)) {\n    throw new TypeError('Expected find and replace tuple or list of tuples')\n  }\n\n  /** @type {FindAndReplaceList} */\n  // @ts-expect-error: correct.\n  const list =\n    !tupleOrList[0] || Array.isArray(tupleOrList[0])\n      ? tupleOrList\n      : [tupleOrList]\n\n  let index = -1\n\n  while (++index < list.length) {\n    const tuple = list[index]\n    result.push([toExpression(tuple[0]), toFunction(tuple[1])])\n  }\n\n  return result\n}\n\n/**\n * Turn a find into an expression.\n *\n * @param {Find} find\n *   Find.\n * @returns {RegExp}\n *   Expression.\n */\nfunction toExpression(find) {\n  return typeof find === 'string' ? new RegExp(escape(find), 'g') : find\n}\n\n/**\n * Turn a replace into a function.\n *\n * @param {Replace} replace\n *   Replace.\n * @returns {ReplaceFunction}\n *   Function.\n */\nfunction toFunction(replace) {\n  return typeof replace === 'function'\n    ? replace\n    : function () {\n        return replace\n      }\n}\n"], "mappings": ";;;;;;AAAe,SAAR,mBAAoC,QAAQ;AAClD,MAAI,OAAO,WAAW,UAAU;AAC/B,UAAM,IAAI,UAAU,mBAAmB;AAAA,EACxC;AAIA,SAAO,OACL,QAAQ,uBAAuB,MAAM,EACrC,QAAQ,MAAM,OAAO;AACxB;;;ACmEO,SAAS,eAAe,MAAM,MAAM,SAAS;AAClD,QAAM,WAAW,WAAW,CAAC;AAC7B,QAAM,UAAU,QAAQ,SAAS,UAAU,CAAC,CAAC;AAC7C,QAAM,QAAQ,QAAQ,IAAI;AAC1B,MAAI,YAAY;AAEhB,SAAO,EAAE,YAAY,MAAM,QAAQ;AACjC,iBAAa,MAAM,QAAQ,OAAO;AAAA,EACpC;AAGA,WAAS,QAAQ,MAAM,SAAS;AAC9B,QAAI,QAAQ;AAEZ,QAAI;AAEJ,WAAO,EAAE,QAAQ,QAAQ,QAAQ;AAC/B,YAAM,SAAS,QAAQ,KAAK;AAE5B,YAAM,WAAW,cAAc,YAAY,WAAW;AAEtD,UACE;AAAA,QACE;AAAA,QACA,WAAW,SAAS,QAAQ,MAAM,IAAI;AAAA,QACtC;AAAA,MACF,GACA;AACA;AAAA,MACF;AAEA,oBAAc;AAAA,IAChB;AAEA,QAAI,aAAa;AACf,aAAO,QAAQ,MAAM,OAAO;AAAA,IAC9B;AAAA,EACF;AAYA,WAAS,QAAQ,MAAM,SAAS;AAC9B,UAAM,SAAS,QAAQ,QAAQ,SAAS,CAAC;AACzC,UAAM,OAAO,MAAM,SAAS,EAAE,CAAC;AAC/B,UAAM,UAAU,MAAM,SAAS,EAAE,CAAC;AAClC,QAAI,QAAQ;AAEZ,UAAM,WAAW,OAAO;AACxB,UAAM,QAAQ,SAAS,QAAQ,IAAI;AACnC,QAAI,SAAS;AAEb,QAAI,QAAQ,CAAC;AAEb,SAAK,YAAY;AAEjB,QAAI,QAAQ,KAAK,KAAK,KAAK,KAAK;AAEhC,WAAO,OAAO;AACZ,YAAM,WAAW,MAAM;AAEvB,YAAM,cAAc;AAAA,QAClB,OAAO,MAAM;AAAA,QACb,OAAO,MAAM;AAAA,QACb,OAAO,CAAC,GAAG,SAAS,IAAI;AAAA,MAC1B;AACA,UAAI,QAAQ,QAAQ,GAAG,OAAO,WAAW;AAEzC,UAAI,OAAO,UAAU,UAAU;AAC7B,gBAAQ,MAAM,SAAS,IAAI,EAAC,MAAM,QAAQ,MAAK,IAAI;AAAA,MACrD;AAGA,UAAI,UAAU,OAAO;AAInB,aAAK,YAAY,WAAW;AAAA,MAC9B,OAAO;AACL,YAAI,UAAU,UAAU;AACtB,gBAAM,KAAK;AAAA,YACT,MAAM;AAAA,YACN,OAAO,KAAK,MAAM,MAAM,OAAO,QAAQ;AAAA,UACzC,CAAC;AAAA,QACH;AAEA,YAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,gBAAM,KAAK,GAAG,KAAK;AAAA,QACrB,WAAW,OAAO;AAChB,gBAAM,KAAK,KAAK;AAAA,QAClB;AAEA,gBAAQ,WAAW,MAAM,CAAC,EAAE;AAC5B,iBAAS;AAAA,MACX;AAEA,UAAI,CAAC,KAAK,QAAQ;AAChB;AAAA,MACF;AAEA,cAAQ,KAAK,KAAK,KAAK,KAAK;AAAA,IAC9B;AAEA,QAAI,QAAQ;AACV,UAAI,QAAQ,KAAK,MAAM,QAAQ;AAC7B,cAAM,KAAK,EAAC,MAAM,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK,EAAC,CAAC;AAAA,MAC3D;AAEA,aAAO,SAAS,OAAO,OAAO,GAAG,GAAG,KAAK;AAAA,IAC3C,OAAO;AACL,cAAQ,CAAC,IAAI;AAAA,IACf;AAEA,WAAO,QAAQ,MAAM;AAAA,EACvB;AACF;AAUA,SAAS,QAAQ,aAAa;AAE5B,QAAM,SAAS,CAAC;AAEhB,MAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAC/B,UAAM,IAAI,UAAU,mDAAmD;AAAA,EACzE;AAIA,QAAM,OACJ,CAAC,YAAY,CAAC,KAAK,MAAM,QAAQ,YAAY,CAAC,CAAC,IAC3C,cACA,CAAC,WAAW;AAElB,MAAI,QAAQ;AAEZ,SAAO,EAAE,QAAQ,KAAK,QAAQ;AAC5B,UAAM,QAAQ,KAAK,KAAK;AACxB,WAAO,KAAK,CAAC,aAAa,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC,CAAC,CAAC;AAAA,EAC5D;AAEA,SAAO;AACT;AAUA,SAAS,aAAa,MAAM;AAC1B,SAAO,OAAO,SAAS,WAAW,IAAI,OAAO,mBAAO,IAAI,GAAG,GAAG,IAAI;AACpE;AAUA,SAAS,WAAW,SAAS;AAC3B,SAAO,OAAO,YAAY,aACtB,UACA,WAAY;AACV,WAAO;AAAA,EACT;AACN;", "names": []}